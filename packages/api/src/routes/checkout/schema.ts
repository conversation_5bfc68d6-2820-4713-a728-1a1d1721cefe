import { z } from "zod";

export const customerDataSchema = z.object({
  name: z.string().min(3, "Nome deve ter pelo menos 3 caracteres"),
  email: z.string().email("Email inválido"),
  phone: z.string().min(10, "Telefone inválido"),
  cpf: z.string().min(11, "CPF inválido"),
});

export const creditCardSchema = z.object({
  cardNumber: z.string().min(16, "Número do cartão inválido"),
  cardHolder: z.string().min(3, "Nome do titular inválido"),
  cardExpiry: z.string().min(5, "Data de validade inválida"),
  cardCvv: z.string().min(3, "CVV inválido"),
  installments: z.number().min(1).max(12),
});

export const checkoutFormSchema = z.object({
  customerData: customerDataSchema,
  paymentMethod: z.enum(["CREDIT_CARD", "PIX", "BOLETO"]),
  creditCard: creditCardSchema.optional(),
  productId: z.string(),
  offerId: z.string().optional(),
  orderBumpIds: z.array(z.string()).optional(),
  couponCode: z.string().optional(),
});

export type CustomerData = z.infer<typeof customerDataSchema>;
export type CreditCardData = z.infer<typeof creditCardSchema>;
export type CheckoutFormData = z.infer<typeof checkoutFormSchema>;

export interface CheckoutProduct {
  id: string;
  title: string;
  description?: string;
  type: "COURSE" | "MENTORING" | "EBOOK";
  price: number;
  installmentsLimit: number;
  enableInstallments?: boolean;
  acceptedPayments?: string[];
  checkoutType?: 'DEFAULT' | 'CUSTOM' | 'EXTERNAL';
  checkoutSettings?: any;
  customCheckoutUrl?: string;
  successUrl?: string;
  cancelUrl?: string;
  termsUrl?: string;
  thumbnail?: string;
  offers?: {
    id: string;
    title: string;
    description?: string;
    price: number;
    type: "ORDER_BUMP" | "UPSELL" | "DOWNSELL";
  }[];
}
