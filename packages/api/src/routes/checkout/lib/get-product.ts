import { Context } from "hono";
import { HTTPException } from "hono/http-exception";
import { db } from "@repo/database";

export async function getProduct(c: Context, productId: string) {
  try {
    const offerId = c.req.query('offer');

    const product = await db.product.findFirst({
      where: {
        id: productId,
        status: 'PUBLISHED',
      },
      include: {
        offers: {
          where: {
            isActive: true,
            OR: [
              { type: 'ORDER_BUMP' },
              ...(offerId ? [{ id: offerId }] : []),
            ],
          },
          select: {
            id: true,
            name: true,
            valueCents: true,
            type: true,
          },
        },
      },
    });

    if (!product) {
      throw new HTTPException(404, { message: 'Produto não encontrado' });
    }

    // If a specific offer is requested, find it and use its price
    let finalPrice = Number(product.priceCents) / 100;
    let selectedOffer = null;

    if (offerId) {
      selectedOffer = product.offers.find(offer => offer.id === offerId);
      if (selectedOffer && selectedOffer.type !== 'ORDER_BUMP') {
        finalPrice = Number(selectedOffer.valueCents) / 100;
      }
    }

    return c.json({
      id: product.id,
      title: product.name,
      description: product.description,
      type: product.type,
      price: finalPrice,
      originalPrice: Number(product.priceCents) / 100,
      installmentsLimit: 12, // Default value
      enableInstallments: true, // Default value
      thumbnail: product.thumbnail,
      checkoutType: product.checkoutType,
      acceptedPayments: ['CREDIT_CARD', 'PIX'], // Default values
      checkoutSettings: product.settings,
      customCheckoutUrl: null,
      successUrl: null,
      cancelUrl: null,
      termsUrl: null,
      selectedOffer: selectedOffer ? {
        id: selectedOffer.id,
        title: selectedOffer.name,
        price: Number(selectedOffer.valueCents) / 100,
        type: selectedOffer.type,
      } : null,
      offers: product.offers?.filter(offer => offer.type === 'ORDER_BUMP').map((offer) => ({
        id: offer.id,
        title: offer.name,
        description: null,
        price: Number(offer.valueCents) / 100,
        type: offer.type,
      })),
    });
  } catch (error) {
    console.error('Error getting product:', error);
    throw new HTTPException(500, { message: 'Erro ao buscar produto' });
  }
}
