import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { db } from "@repo/database";
import { authMiddleware } from "../../middleware/auth";
import { HTTPException } from "hono/http-exception";

const createCheckoutSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  layout: z.enum(["default", "minimal", "modern"]).default("default"),
  paymentMethods: z.array(z.string()).min(1, "Selecione pelo menos um método de pagamento"),
  isActive: z.boolean().default(true),
  allowInstallments: z.boolean().default(true),
  maxInstallments: z.number().min(1).max(12).default(12),
  primaryColor: z.string().optional(),
  showProductImage: z.boolean().default(true),
  showProductDescription: z.boolean().default(true),
  successUrl: z.string().url().optional().or(z.literal("")),
  cancelUrl: z.string().url().optional().or(z.literal("")),
  checkoutType: z.enum(["DEFAULT", "CUSTOM", "EXTERNAL"]).default("DEFAULT"),
  customCheckoutUrl: z.string().url().optional().or(z.literal("")),
  termsUrl: z.string().url().optional().or(z.literal("")),
});

const updateCheckoutSchema = createCheckoutSchema.partial();

export const checkoutsRouter = new Hono()
  .basePath("/products/:productId/checkouts")
  .use("*", authMiddleware)
  
  // Get all checkouts for a product
  .get(
    "/",
    validator(
      "param",
      z.object({
        productId: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Checkouts"],
      summary: "Get product checkouts",
      description: "Get all checkout links for a product",
      responses: {
        200: {
          description: "List of checkout links",
        },
      },
    }),
    async (c) => {
      const { productId } = c.req.valid("param");
      const session = c.get("session");

      // Verify product exists and user has access
      const product = await db.product.findFirst({
        where: {
          id: productId,
          organization: {
            members: {
              some: {
                userId: session.user.id,
              },
            },
          },
        },
      });

      if (!product) {
        throw new HTTPException(404, { message: "Produto não encontrado" });
      }

      const checkouts = await db.checkoutLink.findMany({
        where: {
          productId,
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return c.json({ checkouts });
    },
  )

  // Create new checkout
  .post(
    "/",
    validator(
      "param",
      z.object({
        productId: z.string(),
      }),
    ),
    validator("json", createCheckoutSchema),
    describeRoute({
      tags: ["Checkouts"],
      summary: "Create checkout",
      description: "Create a new checkout link for a product",
      responses: {
        201: {
          description: "Checkout created successfully",
        },
      },
    }),
    async (c) => {
      const { productId } = c.req.valid("param");
      const data = c.req.valid("json");
      const session = c.get("session");

      // Verify product exists and user has access
      const product = await db.product.findFirst({
        where: {
          id: productId,
          organization: {
            members: {
              some: {
                userId: session.user.id,
              },
            },
          },
        },
        include: {
          organization: true,
        },
      });

      if (!product) {
        throw new HTTPException(404, { message: "Produto não encontrado" });
      }

      // Generate checkout URL based on type
      let checkoutUrl: string;
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
      
      if (data.checkoutType === "EXTERNAL" && data.customCheckoutUrl) {
        checkoutUrl = data.customCheckoutUrl;
      } else if (data.checkoutType === "CUSTOM") {
        const params = new URLSearchParams({
          org: product.organizationId,
          layout: data.layout,
          ...(data.primaryColor && { color: data.primaryColor }),
          ...(data.successUrl && { success_url: data.successUrl }),
          ...(data.cancelUrl && { cancel_url: data.cancelUrl }),
          ...(data.termsUrl && { terms_url: data.termsUrl }),
          payments: data.paymentMethods.join(','),
          installments: data.allowInstallments ? data.maxInstallments.toString() : '1',
        });
        checkoutUrl = `${baseUrl}/checkout/${productId}?${params.toString()}`;
      } else {
        checkoutUrl = `${baseUrl}/checkout/${productId}?org=${product.organizationId}`;
      }

      // Store checkout configuration in customParams
      const customParams = {
        name: data.name,
        layout: data.layout,
        paymentMethods: data.paymentMethods,
        isActive: data.isActive,
        allowInstallments: data.allowInstallments,
        maxInstallments: data.maxInstallments,
        primaryColor: data.primaryColor,
        showProductImage: data.showProductImage,
        showProductDescription: data.showProductDescription,
        successUrl: data.successUrl,
        cancelUrl: data.cancelUrl,
        checkoutType: data.checkoutType,
        customCheckoutUrl: data.customCheckoutUrl,
        termsUrl: data.termsUrl,
      };

      const checkout = await db.checkoutLink.create({
        data: {
          productId,
          url: checkoutUrl,
          customParams: JSON.stringify(customParams),
        },
      });

      return c.json({ checkout }, 201);
    },
  )

  // Update checkout
  .put(
    "/:checkoutId",
    validator(
      "param",
      z.object({
        productId: z.string(),
        checkoutId: z.string(),
      }),
    ),
    validator("json", updateCheckoutSchema),
    describeRoute({
      tags: ["Checkouts"],
      summary: "Update checkout",
      description: "Update an existing checkout link",
      responses: {
        200: {
          description: "Checkout updated successfully",
        },
      },
    }),
    async (c) => {
      const { productId, checkoutId } = c.req.valid("param");
      const data = c.req.valid("json");
      const session = c.get("session");

      // Verify checkout exists and user has access
      const checkout = await db.checkoutLink.findFirst({
        where: {
          id: checkoutId,
          productId,
          product: {
            organization: {
              members: {
                some: {
                  userId: session.user.id,
                },
              },
            },
          },
        },
        include: {
          product: {
            include: {
              organization: true,
            },
          },
        },
      });

      if (!checkout) {
        throw new HTTPException(404, { message: "Checkout não encontrado" });
      }

      // Parse existing custom params
      const existingParams = checkout.customParams 
        ? JSON.parse(checkout.customParams) 
        : {};

      // Merge with new data
      const updatedParams = { ...existingParams, ...data };

      // Generate new URL if checkout type or other relevant params changed
      let newUrl = checkout.url;
      if (data.checkoutType || data.customCheckoutUrl || data.layout || data.paymentMethods) {
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
        
        if (updatedParams.checkoutType === "EXTERNAL" && updatedParams.customCheckoutUrl) {
          newUrl = updatedParams.customCheckoutUrl;
        } else if (updatedParams.checkoutType === "CUSTOM") {
          const params = new URLSearchParams({
            org: checkout.product.organizationId,
            layout: updatedParams.layout || "default",
            ...(updatedParams.primaryColor && { color: updatedParams.primaryColor }),
            ...(updatedParams.successUrl && { success_url: updatedParams.successUrl }),
            ...(updatedParams.cancelUrl && { cancel_url: updatedParams.cancelUrl }),
            ...(updatedParams.termsUrl && { terms_url: updatedParams.termsUrl }),
            payments: (updatedParams.paymentMethods || ["credit_card"]).join(','),
            installments: updatedParams.allowInstallments ? (updatedParams.maxInstallments || 12).toString() : '1',
          });
          newUrl = `${baseUrl}/checkout/${productId}?${params.toString()}`;
        } else {
          newUrl = `${baseUrl}/checkout/${productId}?org=${checkout.product.organizationId}`;
        }
      }

      const updatedCheckout = await db.checkoutLink.update({
        where: { id: checkoutId },
        data: {
          url: newUrl,
          customParams: JSON.stringify(updatedParams),
        },
      });

      return c.json({ checkout: updatedCheckout });
    },
  )

  // Delete checkout
  .delete(
    "/:checkoutId",
    validator(
      "param",
      z.object({
        productId: z.string(),
        checkoutId: z.string(),
      }),
    ),
    describeRoute({
      tags: ["Checkouts"],
      summary: "Delete checkout",
      description: "Delete a checkout link",
      responses: {
        200: {
          description: "Checkout deleted successfully",
        },
      },
    }),
    async (c) => {
      const { productId, checkoutId } = c.req.valid("param");
      const session = c.get("session");

      // Verify checkout exists and user has access
      const checkout = await db.checkoutLink.findFirst({
        where: {
          id: checkoutId,
          productId,
          product: {
            organization: {
              members: {
                some: {
                  userId: session.user.id,
                },
              },
            },
          },
        },
      });

      if (!checkout) {
        throw new HTTPException(404, { message: "Checkout não encontrado" });
      }

      await db.checkoutLink.delete({
        where: { id: checkoutId },
      });

      return c.json({ message: "Checkout excluído com sucesso" });
    },
  );
