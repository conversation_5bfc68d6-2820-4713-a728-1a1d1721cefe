# 🚀 **TAREFAS DE IMPLEMENTAÇÃO IMEDIATA - SUPGATEWAY V2**

## 🎯 **TAREFAS PRIORITÁRIAS PARA AGENTES**

### **AGENTE 1: INFRAESTRUTURA E CACHE**

#### **Tarefa 1.1: Configurar Redis Cluster**
```bash
# 1. Adicionar Redis ao docker-compose.yml
# 2. Configurar cluster Redis
# 3. Implementar CacheService
# 4. Integrar com Hono.js
```

**Arquivos para criar/modificar:**
- `docker-compose.yml` - Adicionar serviços Redis
- `packages/cache/index.ts` - CacheService principal
- `packages/cache/redis.ts` - Configuração Redis
- `packages/cache/types.ts` - Tipos do cache
- `packages/api/middleware/cache.ts` - Middleware de cache

**Implementação:**
```typescript
// packages/cache/index.ts
import Redis from 'ioredis';

export class CacheService {
  private redis: Redis.Cluster;

  constructor() {
    this.redis = new Redis.Cluster([
      { host: 'redis-1', port: 6379 },
      { host: 'redis-2', port: 6379 },
      { host: 'redis-3', port: 6379 },
    ]);
  }

  async get(key: string): Promise<any> {
    const value = await this.redis.get(key);
    return value ? JSON.parse(value) : null;
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(value));
  }
}
```

#### **Tarefa 1.2: Implementar Sistema de Leads**
**Arquivos para criar/modificar:**
- `packages/database/prisma/schema.prisma` - Adicionar modelo Lead
- `packages/database/src/lead.ts` - Queries de leads
- `packages/api/src/routes/leads/router.ts` - API de leads
- `packages/api/src/services/lead.ts` - Lógica de negócio

**Schema do Lead:**
```prisma
model Lead {
  id             String   @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])
  name           String
  email          String
  phone          String?
  source         String   // whatsapp, website, referral
  score          Int      @default(0)
  status         LeadStatus @default(NEW)
  metadata       Json?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@map("leads")
}

enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  CONVERTED
  LOST
}
```

---

### **AGENTE 2: INTEGRAÇÃO AGNO E IA**

#### **Tarefa 2.1: Configurar Agno AgentOS**
**Arquivos para criar/modificar:**
- `packages/ai/agno/agentos.ts` - Configuração principal
- `packages/ai/agno/agents/sales.ts` - Agente de vendas
- `packages/ai/agno/agents/support.ts` - Agente de suporte
- `packages/ai/agno/tools/index.ts` - Ferramentas dos agentes
- `packages/ai/agno/workflows/followup.ts` - Workflow de follow-up

**Implementação:**
```typescript
// packages/ai/agno/agentos.ts
import { AgentOS, Agent, Team, Workflow } from '@agno/sdk';

const salesAgent = new Agent({
  name: "SalesMaster",
  model: "claude:sonnet-4",
  tools: [
    new ProductCatalogTool(),
    new LeadQualificationTool(),
    new PricingCalculatorTool(),
    new AppointmentSchedulerTool(),
  ],
  knowledge: Knowledge("sales_knowledge_base"),
  db: Postgres(process.env.DATABASE_URL),
  enable_memories: true,
  instructions: `
    Você é um especialista em vendas de produtos digitais.
    Seu objetivo é qualificar leads e converter em vendas.
  `,
});
```

#### **Tarefa 2.2: Implementar Ferramentas dos Agentes**
**Arquivos para criar:**
- `packages/ai/agno/tools/product-catalog.ts`
- `packages/ai/agno/tools/lead-qualification.ts`
- `packages/ai/agno/tools/pricing-calculator.ts`
- `packages/ai/agno/tools/appointment-scheduler.ts`

---

### **AGENTE 3: TRIGGER.DEV E JOBS**

#### **Tarefa 3.1: Configurar Trigger.dev**
**Arquivos para criar/modificar:**
- `packages/jobs/index.ts` - Jobs principais
- `packages/jobs/process-sale.ts` - Job de processamento
- `packages/jobs/lead-followup.ts` - Job de follow-up
- `packages/jobs/analyze-performance.ts` - Job de análise
- `packages/jobs/data-cleanup.ts` - Job de limpeza

**Implementação:**
```typescript
// packages/jobs/process-sale.ts
import { task } from '@trigger.dev/sdk';

export const processSale = task({
  id: "process-sale",
  retry: {
    maxAttempts: 5,
    minTimeoutInMs: 1000,
    maxTimeoutInMs: 30000,
  },
  run: async ({ orderId, customerId, productId }) => {
    // 1. Processar pagamento
    const payment = await processPayment(orderId);

    // 2. Ativar produto digital
    await activateDigitalProduct(customerId, productId);

    // 3. Enviar confirmação via WhatsApp
    await sendOrderConfirmation(customerId, orderId);

    // 4. Iniciar sequência de onboarding
    await startOnboardingSequence(customerId, productId);

    return { success: true, orderId };
  },
});
```

#### **Tarefa 3.2: Implementar Jobs de Follow-up**
**Arquivos para criar:**
- `packages/jobs/lead-followup.ts`
- `packages/jobs/analyze-performance.ts`
- `packages/jobs/data-cleanup.ts`

---

### **AGENTE 4: WHATSAPP E EVOLUTION API**

#### **Tarefa 4.1: Configurar Evolution API**
**Arquivos para criar/modificar:**
- `packages/whatsapp/evolution.ts` - Cliente Evolution API
- `packages/whatsapp/service.ts` - Serviço WhatsApp
- `packages/whatsapp/templates.ts` - Templates de mensagem
- `packages/whatsapp/webhooks.ts` - Webhooks

**Implementação:**
```typescript
// packages/whatsapp/service.ts
export class WhatsAppService {
  private evolutionAPI: EvolutionAPI;

  constructor() {
    this.evolutionAPI = new EvolutionAPI({
      baseUrl: process.env.EVOLUTION_API_URL,
      apiKey: process.env.EVOLUTION_API_KEY,
      instanceName: process.env.EVOLUTION_INSTANCE_NAME,
    });
  }

  async sendMessage(contactId: string, message: Message): Promise<void> {
    await this.evolutionAPI.sendMessage({
      to: contactId,
      type: message.type,
      content: message.content,
      mediaUrl: message.mediaUrl,
    });
  }

  async handleIncomingMessage(webhook: EvolutionWebhook): Promise<void> {
    const message = await this.processIncomingMessage(webhook);

    const response = await agentOS.process({
      agent: "WhatsAppAgent",
      input: message,
      context: { contactId: message.from },
    });

    await this.sendMessage(message.from, response);
  }
}
```

#### **Tarefa 4.2: Implementar Templates de Mensagem**
**Arquivos para criar:**
- `packages/whatsapp/templates/welcome.ts`
- `packages/whatsapp/templates/followup.ts`
- `packages/whatsapp/templates/purchase-confirmation.ts`
- `packages/whatsapp/templates/support.ts`

---

### **AGENTE 5: MCP SERVER E INTEGRAÇÕES**

#### **Tarefa 5.1: Implementar MCP Server**
**Arquivos para criar/modificar:**
- `packages/mcp/server.ts` - Servidor MCP
- `packages/mcp/tools/index.ts` - Ferramentas MCP
- `packages/mcp/tools/create-lead.ts`
- `packages/mcp/tools/send-whatsapp.ts`
- `packages/mcp/tools/get-product-info.ts`

**Implementação:**
```typescript
// packages/mcp/server.ts
import { MCPServer } from '@supgateway/mcp-server';

const mcpServer = new MCPServer({
  name: 'supgateway-mcp',
  version: '1.0.0',
  tools: [
    {
      name: 'create_lead',
      description: 'Criar um novo lead no sistema',
      inputSchema: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          email: { type: 'string' },
          phone: { type: 'string' },
          source: { type: 'string' },
        },
        required: ['name', 'email', 'phone'],
      },
    },
  ],
});
```

#### **Tarefa 5.2: Implementar Integrações n8n**
**Arquivos para criar:**
- `packages/integrations/n8n/webhooks.ts`
- `packages/integrations/n8n/workflows.ts`
- `packages/integrations/zapier/webhooks.ts`

---

## 📋 **CHECKLIST DE IMPLEMENTAÇÃO**

### **Dia 1 (Hoje)**
- [ ] **Agente 1**: Configurar Redis Cluster
- [ ] **Agente 2**: Instalar e configurar Agno
- [ ] **Agente 3**: Configurar Trigger.dev
- [ ] **Agente 4**: Configurar Evolution API
- [ ] **Agente 5**: Implementar MCP Server básico

### **Dia 2-3**
- [ ] **Agente 1**: Implementar sistema de leads
- [ ] **Agente 2**: Criar agentes de IA básicos
- [ ] **Agente 3**: Implementar jobs de processamento
- [ ] **Agente 4**: Implementar WhatsApp service
- [ ] **Agente 5**: Criar integrações n8n

### **Dia 4-5**
- [ ] **Agente 1**: Integrar cache com API
- [ ] **Agente 2**: Implementar workflows de follow-up
- [ ] **Agente 3**: Configurar retry e error handling
- [ ] **Agente 4**: Criar templates de mensagem
- [ ] **Agente 5**: Documentar MCP Server

---

## 🔧 **COMANDOS PARA EXECUÇÃO**

### **Instalação de Dependências**
```bash
# Redis
pnpm add ioredis @types/ioredis

# Agno
pnpm add @agno/sdk

# Trigger.dev
pnpm add @trigger.dev/sdk

# Evolution API
pnpm add evolution-api

# MCP Server
pnpm add @supgateway/mcp-server
```

### **Configuração Docker**
```yaml
# docker-compose.yml
version: '3.8'
services:
  redis-1:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  redis-2:
    image: redis:7-alpine
    ports:
      - "6380:6379"

  redis-3:
    image: redis:7-alpine
    ports:
      - "6381:6379"
```

### **Variáveis de Ambiente**
```env
# Redis
REDIS_URL=redis://localhost:6379
REDIS_CLUSTER_NODES=redis://localhost:6379,redis://localhost:6380,redis://localhost:6381

# Agno
AGNO_API_KEY=your_agno_api_key
AGNO_AGENT_ID=your_agent_id

# Trigger.dev
TRIGGER_SECRET_KEY=your_trigger_secret_key
TRIGGER_API_URL=https://api.trigger.dev

# Evolution API
EVOLUTION_API_URL=https://your-evolution-api.com
EVOLUTION_API_KEY=your_evolution_api_key
EVOLUTION_INSTANCE_NAME=supgateway

# MCP Server
MCP_SERVER_PORT=3001
MCP_SERVER_HOST=localhost
```

---

## 🎯 **MÉTRICAS DE SUCESSO**

### **Técnicas**
- [ ] Redis Cluster funcionando (3 nodes)
- [ ] Agno AgentOS respondendo < 100ms
- [ ] Trigger.dev jobs executando sem erro
- [ ] Evolution API enviando mensagens
- [ ] MCP Server respondendo a requests

### **Funcionais**
- [ ] Sistema de leads criando registros
- [ ] Agentes de IA respondendo adequadamente
- [ ] Jobs processando vendas automaticamente
- [ ] WhatsApp enviando mensagens
- [ ] Integrações funcionando via MCP

---

## 📞 **COMUNICAÇÃO ENTRE AGENTES**

### **Canal Principal**
- **Slack/Discord**: #supgateway-v2-implementation
- **Frequência**: Updates diários às 18h
- **Formato**: Status + Bloqueios + Próximos passos

### **Reuniões**
- **Daily Standup**: 9h (15 min)
- **Weekly Review**: Sexta 16h (30 min)
- **Sprint Planning**: Segunda 10h (1h)

### **Documentação**
- **Confluence/Notion**: Documentação técnica
- **GitHub**: Issues e PRs
- **Figma**: Design e protótipos

---

**Este documento serve como guia prático para os agentes começarem a implementação imediatamente. Cada agente tem responsabilidades claras e métricas de sucesso definidas.**
