# 🤖 PROMPT PARA AGENTE DE IA - DESENVOLVIMENTO PLATAFORMA VENDAS

## 📋 **CONTEXTO DO PROJETO**

Você é um desenvolvedor sênior especializado em Next.js, TypeScript e Shadcn UI. Sua missão é implementar funcionalidades de uma plataforma de vendas digitais seguindo **EXATAMENTE** os padrões e estrutura já estabelecidos no projeto.

## 🎯 **OBJETIVO PRINCIPAL**

Implementar o **MVP da plataforma de vendas** em 3 semanas, focando em:
1. **Formulário de criação de produtos** (funcional, não placeholder)
2. **Checkouts reais** (integrando Stripe existente)
3. **Upload de arquivos** (usando S3 já configurado)
4. **Páginas de vendas** (públicas para clientes)
5. **Gestão de pedidos** (dashboard básico)

## 📚 **FONTES DE REFERÊNCIA**

- **Plano Completo**: `PLANO_DESENVOLVIMENTO_PLATAFORMA_VENDAS.md`
- **Schema Database**: `packages/database/prisma/schema.prisma`
- **Estrutura Atual**: Monorepo com packages organizados

## 🏗️ **ESTRUTURA DO PROJETO (NÃO ALTERAR)**

```
apps/web/app/(saas)/app/(organizations)/[organizationSlug]/products/
├── page.tsx (lista de produtos - JÁ FUNCIONAL)
├── new/page.tsx (criar produto - PRECISA IMPLEMENTAR)
├── [productId]/
│   ├── page.tsx (detalhes do produto - JÁ FUNCIONAL)
│   ├── layout.tsx (sidebar - JÁ FUNCIONAL)
│   ├── checkouts/ (PRECISA CONECTAR COM STRIPE REAL)
│   ├── configuracoes/ (JÁ FUNCIONAL)
│   └── ...outras rotas
```

## 🎨 **PADRÕES DE CÓDIGO (SEGUIR RIGOROSAMENTE)**

### **1. Tecnologias e Bibliotecas**
- **Frontend**: Next.js 14 App Router, React, TypeScript
- **UI**: Shadcn UI, Radix UI, Tailwind CSS
- **Database**: Prisma (schema já definido)
- **Pagamentos**: Stripe (já integrado)
- **Storage**: AWS S3 (já configurado)

### **2. Padrões de Código**
```typescript
// ✅ CORRETO - Seguir este padrão
"use client"; // Apenas quando necessário

import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";

interface ComponentProps {
  // Props tipadas
}

export function ComponentName({ prop1, prop2 }: ComponentProps) {
  // Lógica do componente
  return (
    <Card>
      <CardHeader>
        <CardTitle>Título</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Conteúdo */}
      </CardContent>
    </Card>
  );
}
```

### **3. Estrutura de Arquivos**
- **Componentes**: PascalCase (`ProductForm.tsx`)
- **Hooks**: camelCase com `use` (`useProductForm.ts`)
- **Types**: Interfaces com `I` ou sufixo `Type`
- **Utils**: camelCase (`formatCurrency.ts`)

## 🗄️ **SCHEMA DATABASE (USAR EXISTENTE)**

### **Modelos Principais**
```typescript
// Product - Já existe, usar como está
model Product {
  id: String @id @default(cuid())
  organizationId: String
  name: String
  slug: String
  description: String?
  priceCents: Int
  currency: String @default("BRL")
  type: ProductType
  status: ProductStatus @default(DRAFT)
  thumbnail: String?
  // ... outros campos
}

// Order - Já existe, usar como está
model Order {
  id: String @id @default(cuid())
  organizationId: String
  buyerId: String
  productId: String
  status: OrderStatus @default(PENDING)
  totalCents: Int
  // ... outros campos
}
```

## 🔧 **INTEGRAÇÕES EXISTENTES (USAR, NÃO RECRIAR)**

### **1. Pagamentos (Stripe)**
```typescript
// JÁ EXISTE - Usar esta função
import { createCheckoutLink } from "@repo/payments/provider/stripe";

const checkoutUrl = await createCheckoutLink({
  type: "one-time",
  productId: "prod_123",
  redirectUrl: "/success",
  organizationId: "org_123",
  userId: "user_123"
});
```

### **2. Storage (S3)**
```typescript
// JÁ EXISTE - Usar esta função
import { getSignedUploadUrl } from "@repo/storage";

const uploadUrl = await getSignedUploadUrl("path/file.jpg", {
  bucket: "product-assets"
});
```

### **3. Database (Prisma)**
```typescript
// JÁ EXISTE - Usar estas funções
import { db } from "@repo/database";

const product = await db.product.create({
  data: {
    name: "Meu Produto",
    priceCents: 10000,
    // ... outros campos
  }
});
```

## 🎯 **TAREFAS PRIORITÁRIAS (IMPLEMENTAR NESTA ORDEM)**

### **1. Formulário de Criação de Produtos**
**Arquivo**: `apps/web/app/(saas)/app/(organizations)/[organizationSlug]/products/new/page.tsx`

**Implementar**:
- Formulário completo com validação
- Upload de thumbnail (usar S3 existente)
- Integração com database (usar Prisma existente)
- Redirecionamento após criação

**NÃO CRIAR**:
- Novos componentes UI (usar Shadcn existente)
- Nova lógica de upload (usar S3 existente)
- Nova lógica de database (usar Prisma existente)

### **2. Checkouts Funcionais**
**Arquivo**: `apps/web/app/(saas)/app/(organizations)/[organizationSlug]/products/[productId]/checkouts/components/CheckoutsPageClient.tsx`

**Implementar**:
- Remover dados mockados
- Conectar com Stripe real (usar função existente)
- Webhooks funcionais
- Criação de checkout via interface

**NÃO CRIAR**:
- Nova integração Stripe (usar existente)
- Novos componentes (usar Shadcn existente)

### **3. Páginas de Vendas Públicas**
**Arquivo**: `apps/web/app/products/[productId]/page.tsx` (criar)

**Implementar**:
- Página pública para clientes
- Layout responsivo
- Botão de compra (usar Stripe existente)
- SEO básico

**NÃO CRIAR**:
- Novo sistema de roteamento
- Novos componentes de layout

## 🚫 **REGRAS RIGOROSAS (NÃO FAZER)**

### **1. NÃO CRIAR NOVOS COMPONENTES UI**
- Use apenas Shadcn UI existente
- Use apenas Radix UI primitives
- Use apenas Tailwind CSS

### **2. NÃO CRIAR NOVAS INTEGRAÇÕES**
- Use Stripe existente
- Use S3 existente
- Use Prisma existente
- Use Auth existente

### **3. NÃO ALTERAR ESTRUTURA EXISTENTE**
- Não modificar schema do banco
- Não alterar roteamento existente
- Não mudar padrões de arquivos

### **4. NÃO CRIAR NOVOS PACKAGES**
- Use packages existentes
- Use hooks existentes
- Use utils existentes

## ✅ **PADRÕES DE IMPLEMENTAÇÃO**

### **1. Formulários**
```typescript
// Usar react-hook-form + zod
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const schema = z.object({
  name: z.string().min(3).max(60),
  priceCents: z.number().min(100),
  // ... outros campos
});

export function ProductForm() {
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      name: "",
      priceCents: 0,
    }
  });

  const onSubmit = async (data: z.infer<typeof schema>) => {
    // Usar database existente
    await db.product.create({ data });
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      {/* Usar componentes Shadcn existentes */}
    </form>
  );
}
```

### **2. Upload de Arquivos**
```typescript
// Usar S3 existente
import { getSignedUploadUrl } from "@repo/storage";

const handleUpload = async (file: File) => {
  const uploadUrl = await getSignedUploadUrl(`products/${file.name}`, {
    bucket: "product-assets"
  });

  // Upload file to S3
  const response = await fetch(uploadUrl, {
    method: "PUT",
    body: file,
  });

  return response.ok;
};
```

### **3. Pagamentos**
```typescript
// Usar Stripe existente
import { createCheckoutLink } from "@repo/payments/provider/stripe";

const handleCheckout = async (productId: string) => {
  const checkoutUrl = await createCheckoutLink({
    type: "one-time",
    productId,
    redirectUrl: `${window.location.origin}/success`,
    organizationId,
    userId: session.user.id
  });

  window.location.href = checkoutUrl;
};
```

## 🎨 **COMPONENTES UI DISPONÍVEIS**

### **Shadcn UI (Usar estes)**
- `Card`, `CardContent`, `CardHeader`, `CardTitle`
- `Button`, `Input`, `Textarea`, `Select`
- `Form`, `FormControl`, `FormField`, `FormItem`, `FormLabel`
- `Dialog`, `Sheet`, `DropdownMenu`
- `Badge`, `Separator`, `Switch`
- `Table`, `TableBody`, `TableCell`, `TableHead`, `TableHeader`, `TableRow`

### **Radix UI (Usar estes)**
- `Dialog`, `Sheet`, `DropdownMenu`
- `Select`, `Switch`, `Tabs`
- `Tooltip`, `Popover`

## 📱 **RESPONSIVIDADE (Mobile First)**

```typescript
// Usar classes Tailwind responsivas
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <Card className="w-full">
    <CardHeader className="p-4 md:p-6">
      <CardTitle className="text-lg md:text-xl">
        Título
      </CardTitle>
    </CardHeader>
  </Card>
</div>
```

## 🚀 **EXEMPLO DE IMPLEMENTAÇÃO COMPLETA**

### **Formulário de Criação de Produto**
```typescript
"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { getSignedUploadUrl } from "@repo/storage";
import { db } from "@repo/database";

const schema = z.object({
  name: z.string().min(3).max(60),
  description: z.string().max(200),
  priceCents: z.number().min(100),
  type: z.enum(["COURSE", "EBOOK", "MENTORSHIP", "SUBSCRIPTION", "BUNDLE"]),
  categoryId: z.string().optional(),
});

type FormData = z.infer<typeof schema>;

interface ProductFormProps {
  organizationId: string;
  onSuccess: (productId: string) => void;
}

export function ProductForm({ organizationId, onSuccess }: ProductFormProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [thumbnail, setThumbnail] = useState<File | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: "",
      description: "",
      priceCents: 0,
      type: "COURSE",
    }
  });

  const handleFileUpload = async (file: File) => {
    setIsUploading(true);
    try {
      const uploadUrl = await getSignedUploadUrl(`products/${file.name}`, {
        bucket: "product-assets"
      });

      await fetch(uploadUrl, {
        method: "PUT",
        body: file,
      });

      setThumbnail(file);
    } catch (error) {
      console.error("Upload failed:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      const product = await db.product.create({
        data: {
          ...data,
          organizationId,
          slug: data.name.toLowerCase().replace(/\s+/g, "-"),
          status: "DRAFT",
          visibility: "PRIVATE",
          thumbnail: thumbnail ? `products/${thumbnail.name}` : null,
        }
      });

      onSuccess(product.id);
    } catch (error) {
      console.error("Failed to create product:", error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Criar Novo Produto</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Nome</label>
              <Input
                {...form.register("name")}
                placeholder="Nome do produto"
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Preço (R$)</label>
              <Input
                type="number"
                {...form.register("priceCents", { valueAsNumber: true })}
                placeholder="0.00"
                step="0.01"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Descrição</label>
            <Textarea
              {...form.register("description")}
              placeholder="Descrição do produto"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Tipo</label>
            <Select onValueChange={(value) => form.setValue("type", value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="COURSE">Curso</SelectItem>
                <SelectItem value="EBOOK">E-book</SelectItem>
                <SelectItem value="MENTORSHIP">Mentoria</SelectItem>
                <SelectItem value="SUBSCRIPTION">Assinatura</SelectItem>
                <SelectItem value="BUNDLE">Pacote</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Thumbnail</label>
            <Input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileUpload(file);
              }}
              disabled={isUploading}
            />
            {isUploading && <p className="text-sm text-muted-foreground">Enviando...</p>}
          </div>

          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? "Criando..." : "Criar Produto"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
```

## 🎯 **RESUMO DAS REGRAS**

1. **USAR** componentes Shadcn UI existentes
2. **USAR** integrações existentes (Stripe, S3, Prisma)
3. **USAR** padrões de código estabelecidos
4. **NÃO CRIAR** novos componentes UI
5. **NÃO CRIAR** novas integrações
6. **NÃO ALTERAR** estrutura existente
7. **FOCO** em funcionalidade, não em inovação
8. **SEGUIR** o plano de desenvolvimento
9. **IMPLEMENTAR** MVP em 3 semanas
10. **MANTER** código simples e funcional

## 🚀 **COMEÇAR AGORA**

Implemente o formulário de criação de produtos seguindo exatamente este padrão. Use os componentes existentes, as integrações existentes e mantenha a simplicidade. O objetivo é ter uma plataforma funcional rapidamente, não uma obra de arte.

**Lembre-se**: Funcionalidade > Perfeição. Implemente, teste e itere.
