# 🤖 PROMPT CONCISO - DESENVOLVIMENTO PLATAFORMA VENDAS

## 🎯 **MISSÃO**
Implementar MVP de plataforma de vendas em 3 semanas usando **EXATAMENTE** a estrutura e padrões existentes.

## 📚 **REFERÊNCIAS**
- **Plano**: `PLANO_DESENVOLVIMENTO_PLATAFORMA_VENDAS.md`
- **Schema**: `packages/database/prisma/schema.prisma`
- **Padrões**: `.cursor/rules/` (TypeScript, Next.js, Shadcn UI)

## 🏗️ **ESTRUTURA ATUAL (NÃO ALTERAR)**
```
apps/web/app/(saas)/app/(organizations)/[organizationSlug]/products/
├── page.tsx (✅ funcional)
├── new/page.tsx (❌ placeholder - IMPLEMENTAR)
├── [productId]/
│   ├── page.tsx (✅ funcional)
│   ├── layout.tsx (✅ funcional)
│   ├── checkouts/ (❌ mockado - CONECTAR STRIPE)
│   └── configuracoes/ (✅ funcional)
```

## 🎨 **PADRÕES RIGOROSOS**

### **Tecnologias**
- Next.js 14 App Router, TypeScript, React
- Shadcn UI + Radix UI + Tailwind CSS
- Prisma + PostgreSQL
- Stripe (já integrado)
- AWS S3 (já configurado)

### **Código**
```typescript
// ✅ PADRÃO CORRETO
"use client"; // apenas quando necessário

import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";

interface Props {
  // Props tipadas
}

export function ComponentName({ prop1, prop2 }: Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Título</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Conteúdo */}
      </CardContent>
    </Card>
  );
}
```

## 🔧 **INTEGRAÇÕES EXISTENTES (USAR)**

### **Database**
```typescript
import { db } from "@repo/database";

// Criar produto
const product = await db.product.create({
  data: { name, priceCents, organizationId, /* ... */ }
});

// Buscar produtos
const products = await db.product.findMany({
  where: { organizationId }
});
```

### **Stripe**
```typescript
import { createCheckoutLink } from "@repo/payments/provider/stripe";

const checkoutUrl = await createCheckoutLink({
  type: "one-time",
  productId,
  redirectUrl: "/success",
  organizationId,
  userId
});
```

### **S3 Storage**
```typescript
import { getSignedUploadUrl } from "@repo/storage";

const uploadUrl = await getSignedUploadUrl(`products/${filename}`, {
  bucket: "product-assets"
});
```

## 🎯 **TAREFAS PRIORITÁRIAS**

### **1. Formulário de Criação (URGENTE)**
**Arquivo**: `apps/web/app/(saas)/app/(organizations)/[organizationSlug]/products/new/page.tsx`

**Implementar**:
- Formulário com validação (react-hook-form + zod)
- Campos: nome, descrição, preço, tipo, categoria, thumbnail
- Upload S3 para thumbnail
- Integração Prisma para salvar
- Redirecionamento após criação

**Componentes Shadcn**: `Card`, `Input`, `Textarea`, `Select`, `Button`, `Form`

### **2. Checkouts Reais (URGENTE)**
**Arquivo**: `apps/web/app/(saas)/app/(organizations)/[organizationSlug]/products/[productId]/checkouts/components/CheckoutsPageClient.tsx`

**Implementar**:
- Remover dados mockados
- Conectar com Stripe real
- Botão "Criar Checkout" funcional
- Webhooks de confirmação

### **3. Páginas de Vendas (ALTA)**
**Arquivo**: `apps/web/app/products/[productId]/page.tsx` (criar)

**Implementar**:
- Página pública para clientes
- Layout responsivo
- Botão de compra (Stripe)
- SEO básico

### **4. Gestão de Pedidos (MÉDIA)**
**Arquivo**: `apps/web/app/(saas)/app/(organizations)/[organizationSlug]/orders/page.tsx` (criar)

**Implementar**:
- Lista de pedidos
- Filtros básicos
- Detalhes do pedido

## 🚫 **REGRAS RIGOROSAS**

### **NÃO FAZER**
- ❌ Criar novos componentes UI (usar Shadcn existente)
- ❌ Criar novas integrações (usar Stripe/S3/Prisma existentes)
- ❌ Alterar schema do banco
- ❌ Modificar estrutura de arquivos
- ❌ Criar novos packages

### **FAZER**
- ✅ Usar componentes Shadcn existentes
- ✅ Usar integrações existentes
- ✅ Seguir padrões de código
- ✅ Manter simplicidade
- ✅ Focar em funcionalidade

## 📱 **RESPONSIVIDADE**
```typescript
// Mobile-first com Tailwind
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <Card className="w-full">
    <CardHeader className="p-4 md:p-6">
      <CardTitle className="text-lg md:text-xl">Título</CardTitle>
    </CardHeader>
  </Card>
</div>
```

## 🎯 **EXEMPLO RÁPIDO - Formulário de Produto**

```typescript
"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { getSignedUploadUrl } from "@repo/storage";
import { db } from "@repo/database";

const schema = z.object({
  name: z.string().min(3).max(60),
  description: z.string().max(200),
  priceCents: z.number().min(100),
  type: z.enum(["COURSE", "EBOOK", "MENTORSHIP", "SUBSCRIPTION", "BUNDLE"]),
});

export function ProductForm({ organizationId, onSuccess }) {
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: { name: "", description: "", priceCents: 0, type: "COURSE" }
  });

  const onSubmit = async (data) => {
    const product = await db.product.create({
      data: { ...data, organizationId, slug: data.name.toLowerCase().replace(/\s+/g, "-") }
    });
    onSuccess(product.id);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Criar Produto</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <Input {...form.register("name")} placeholder="Nome" />
          <Textarea {...form.register("description")} placeholder="Descrição" />
          <Input type="number" {...form.register("priceCents", { valueAsNumber: true })} placeholder="Preço" />
          <Select onValueChange={(value) => form.setValue("type", value)}>
            <SelectTrigger><SelectValue placeholder="Tipo" /></SelectTrigger>
            <SelectContent>
              <SelectItem value="COURSE">Curso</SelectItem>
              <SelectItem value="EBOOK">E-book</SelectItem>
            </SelectContent>
          </Select>
          <Button type="submit">Criar</Button>
        </form>
      </CardContent>
    </Card>
  );
}
```

## 🚀 **COMEÇAR AGORA**

1. **Implemente o formulário de criação** seguindo o exemplo
2. **Use componentes Shadcn existentes**
3. **Conecte com Stripe real** nos checkouts
4. **Mantenha simplicidade** e funcionalidade
5. **Foque no MVP** - funcional > perfeito

**Lembre-se**: Funcionalidade > Perfeição. Implemente, teste, itere.
