import { getSession } from "@saas/auth/lib/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import {
	UsersIcon,
	BuildingIcon,
	CreditCardIcon,
	ActivityIcon,
	TrendingUpIcon,
	DollarSignIcon,
	ArrowUpIcon,
	ArrowDownIcon,
	CalendarIcon,
	ClockIcon,
	CheckCircleIcon,
	AlertCircleIcon,
	BarChart3Icon,
	WebhookIcon,
	FileTextIcon,
	SettingsIcon
} from "lucide-react";
import { redirect } from "next/navigation";
import { countAllUsers, getOrganizations } from "@repo/database";
import { db } from "@repo/database/prisma/client";
import { isAdmin } from "@repo/auth/lib/helper";
import Link from "next/link";

export default async function AdminPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	if (!isAdmin(session.user)) {
		redirect("/app");
	}

	const [organizations, totalUsers, totalTransactions, monthlyRevenue] = await Promise.all([
		getOrganizations({ limit: 1000, offset: 0 }),
		countAllUsers(),
		db.transaction.count(),
		db.transaction.aggregate({
			where: {
				createdAt: {
					gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
				},
				status: "COMPLETED",
			},
			_sum: { amountCents: true },
		}),
	]);

	const stats = [
		{
			title: "Organizações",
			value: organizations.length,
			description: "Total de organizações",
			icon: BuildingIcon,
			trend: "+12%",
			trendUp: true,
			href: "/app/backoffice/organizations"
		},
		{
			title: "Usuários",
			value: totalUsers,
			description: "Total de usuários",
			icon: UsersIcon,
			trend: "+8%",
			trendUp: true,
			href: "/app/backoffice/users"
		},
		{
			title: "Transações",
			value: totalTransactions,
			description: "Total de transações",
			icon: CreditCardIcon,
			trend: "+23%",
			trendUp: true,
			href: "/app/backoffice/transactions"
		},
		{
			title: "Receita Mensal",
			value: `R$ ${((monthlyRevenue._sum?.amountCents || 0) / 100).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
			description: "Receita do mês atual",
			icon: DollarSignIcon,
			trend: "+15%",
			trendUp: true,
			href: "/app/backoffice/analytics"
		},
	];

	// Dados simulados para atividades recentes
	const recentActivities = [
		{
			id: 1,
			type: "organization_created",
			title: "Nova organização criada",
			description: "Academia Digital - há 2 horas",
			icon: BuildingIcon,
			color: "bg-green-500"
		},
		{
			id: 2,
			type: "user_registered",
			title: "Usuário registrado",
			description: "João Silva - há 4 horas",
			icon: UsersIcon,
			color: "bg-blue-500"
		},
		{
			id: 3,
			type: "transaction_processed",
			title: "Transação processada",
			description: "R$ 297,00 - há 6 horas",
			icon: CreditCardIcon,
			color: "bg-yellow-500"
		},
		{
			id: 4,
			type: "payment_failed",
			title: "Pagamento falhou",
			description: "R$ 150,00 - há 8 horas",
			icon: AlertCircleIcon,
			color: "bg-red-500"
		},
		{
			id: 5,
			type: "withdrawal_requested",
			title: "Saque solicitado",
			description: "R$ 2.500,00 - há 12 horas",
			icon: TrendingUpIcon,
			color: "bg-purple-500"
		}
	];

	// Dados simulados para métricas de crescimento
	const growthMetrics = [
		{
			label: "Novos usuários (7 dias)",
			value: "+24",
			trend: "up",
			color: "text-green-500"
		},
		{
			label: "Organizações ativas",
			value: "12",
			trend: "stable",
			color: "text-blue-500"
		},
		{
			label: "Taxa de conversão",
			value: "3.2%",
			trend: "up",
			color: "text-purple-500"
		},
		{
			label: "Receita total",
			value: "R$ 15.420",
			trend: "up",
			color: "text-green-500"
		}
	];

	// Links rápidos para ações administrativas
	const quickActions = [
		{
			title: "Ver Transações",
			description: "Gerenciar pagamentos",
			icon: CreditCardIcon,
			href: "/app/backoffice/transactions",
			color: "bg-blue-500"
		},
		{
			title: "Configurar Gateways",
			description: "Gerenciar gateways",
			icon: WebhookIcon,
			href: "/app/backoffice/gateways",
			color: "bg-green-500"
		},
		{
			title: "Ver Analytics",
			description: "Relatórios detalhados",
			icon: BarChart3Icon,
			href: "/app/backoffice/analytics",
			color: "bg-purple-500"
		},
		{
			title: "Configurações",
			description: "Configurações do sistema",
			icon: SettingsIcon,
			href: "/app/backoffice/settings",
			color: "bg-gray-500"
		}
	];

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-3xl font-bold">Dashboard Administrativo</h1>
				<p className="text-muted-foreground">
					Visão geral do sistema e métricas principais
				</p>
			</div>

			{/* Stats Grid */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				{stats.map((stat) => {
					const Icon = stat.icon;
					return (
						<Link key={stat.title} href={stat.href}>
							<Card className="hover:shadow-md transition-shadow cursor-pointer">
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">
										{stat.title}
									</CardTitle>
									<Icon className="h-4 w-4 text-muted-foreground" />
								</CardHeader>
								<CardContent>
									<div className="text-2xl font-bold">{stat.value}</div>
									<p className="text-xs text-muted-foreground">
										{stat.description}
									</p>
									<div className="flex items-center pt-1">
										{stat.trendUp ? (
											<ArrowUpIcon className="h-3 w-3 text-green-500" />
										) : (
											<ArrowDownIcon className="h-3 w-3 text-red-500" />
										)}
										<span className={`text-xs ml-1 ${stat.trendUp ? 'text-green-500' : 'text-red-500'}`}>
											{stat.trend}
										</span>
										<span className="text-xs text-muted-foreground ml-1">
											vs mês anterior
										</span>
									</div>
								</CardContent>
							</Card>
						</Link>
					);
				})}
			</div>

			{/* Quick Actions */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<SettingsIcon className="h-5 w-5" />
						Ações Rápidas
					</CardTitle>
					<CardDescription>
						Acesso rápido às principais funcionalidades administrativas
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
						{quickActions.map((action) => {
							const Icon = action.icon;
							return (
								<Link key={action.title} href={action.href}>
									<Card className="hover:shadow-md transition-shadow cursor-pointer">
										<CardContent className="p-4">
											<div className="flex items-center space-x-3">
												<div className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center`}>
													<Icon className="h-5 w-5 text-white" />
												</div>
												<div>
													<h3 className="font-medium">{action.title}</h3>
													<p className="text-sm text-muted-foreground">{action.description}</p>
												</div>
											</div>
										</CardContent>
									</Card>
								</Link>
							);
						})}
					</div>
				</CardContent>
			</Card>

			{/* Recent Activity and Growth Metrics */}
			<div className="grid gap-4 md:grid-cols-2">
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<ActivityIcon className="h-5 w-5" />
							Atividade Recente
						</CardTitle>
						<CardDescription>
							Últimas ações no sistema
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{recentActivities.map((activity) => {
								const Icon = activity.icon;
								return (
									<div key={activity.id} className="flex items-center space-x-4">
										<div className={`w-2 h-2 ${activity.color} rounded-full`}></div>
										<div className="flex-1 space-y-1">
											<p className="text-sm font-medium">{activity.title}</p>
											<p className="text-xs text-muted-foreground">{activity.description}</p>
										</div>
										<Icon className="h-4 w-4 text-muted-foreground" />
									</div>
								);
							})}
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<TrendingUpIcon className="h-5 w-5" />
							Métricas de Crescimento
						</CardTitle>
						<CardDescription>
							Performance do sistema
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							{growthMetrics.map((metric, index) => (
								<div key={index} className="flex justify-between items-center">
									<span className="text-sm">{metric.label}</span>
									<div className="flex items-center gap-2">
										<span className={`text-sm font-medium ${metric.color}`}>
											{metric.value}
										</span>
										{metric.trend === "up" && (
											<ArrowUpIcon className="h-3 w-3 text-green-500" />
										)}
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
