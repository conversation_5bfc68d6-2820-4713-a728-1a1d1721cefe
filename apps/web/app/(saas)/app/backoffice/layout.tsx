import { getSession } from "@saas/auth/lib/server";
import { redirect } from "next/navigation";
import { isAdmin } from "@repo/auth/lib/helper";
import { AppWrapper } from "@saas/shared/components/AppWrapper";

export default async function BackofficeLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	if (!isAdmin(session.user)) {
		redirect("/app");
	}

	return (
		<AppWrapper>
			<main className="flex-1">
				{children}
			</main>
		</AppWrapper>
	);
}
