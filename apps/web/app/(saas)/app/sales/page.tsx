import { getSession } from "@saas/auth/lib/server";
import { isAdmin } from "@repo/auth/lib/helper";
import { redirect } from "next/navigation";
import { db } from "@repo/database/prisma/client";

export default async function SalesPage() {
	const session = await getSession();

	if (!session) {
		redirect("/auth/login");
	}

	// This route should not be accessible - redirect to organization-scoped sales
	// For admins, redirect to admin panel
	if (isAdmin(session.user)) {
		redirect("/app/admin");
	}

	// For regular users, find their organization and redirect to organization-scoped sales
	const userMembership = await db.member.findFirst({
		where: {
			userId: session.user.id,
		},
		include: {
			organization: true,
		},
	});

	if (!userMembership) {
		// If user has no organization membership, redirect to create organization
		redirect("/new-organization");
	}

	// Redirect to organization-scoped sales
	redirect(`/app/${userMembership.organization.slug}/sales`);
}
