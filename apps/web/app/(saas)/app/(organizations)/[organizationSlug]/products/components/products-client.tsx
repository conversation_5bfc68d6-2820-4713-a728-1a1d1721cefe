"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { PageLayout } from "@ui/components/page-layout";
import {
	PlusIcon,
	SearchIcon,
	FilterIcon,
	EyeIcon,
	EditIcon,
	MoreHorizontalIcon,
	ImageIcon,
	UsersIcon,
	DollarSignIcon,
	Grid3X3Icon,
	ListIcon,
	BarChart3Icon,
	TrendingUpIcon,
	CalendarIcon,
	StarIcon,
	PlayCircleIcon,
	BookOpenIcon,
	GraduationCapIcon,
	PackageIcon,
	CopyIcon,
	ArchiveIcon,
	TrashIcon,
	Settings2Icon
} from "lucide-react";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Label } from "@ui/components/label";
import { Separator } from "@ui/components/separator";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { useProducts, useDeleteProduct, useUpdateProductStatus } from "@saas/products/hooks/useProductsApi";
import { Product } from "@saas/products/hooks/useProductsApi";
import { SimpleCreateProductSheet } from "./SimpleCreateProductSheet";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";

interface ProductsClientProps {
	organizationId: string;
	userId: string;
	organizationSlug?: string;
}

export function ProductsClient({ organizationId, userId, organizationSlug }: ProductsClientProps) {
	const [search, setSearch] = useState("");
	const [statusFilter, setStatusFilter] = useState<string | undefined>();
	const [typeFilter, setTypeFilter] = useState<string | undefined>();
	const [page, setPage] = useState(1);
	const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

	const { data: productsData, isLoading, error } = useProducts(organizationId, {
		page,
		limit: 10,
		search: search || undefined,
		status: statusFilter as any,
		type: typeFilter as any,
	});


	const deleteProduct = useDeleteProduct();
	const updateStatus = useUpdateProductStatus();

	const products = productsData?.products || [];
	const pagination = productsData?.pagination;

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case 'PUBLISHED':
				return 'default';
			case 'DRAFT':
				return 'secondary';
			case 'ARCHIVED':
				return 'outline';
			case 'SUSPENDED':
				return 'destructive';
			default:
				return 'outline';
		}
	};

	const getStatusLabel = (status: string) => {
		switch (status) {
			case 'PUBLISHED':
				return 'Published';
			case 'DRAFT':
				return 'Draft';
			case 'ARCHIVED':
				return 'Archived';
			case 'SUSPENDED':
				return 'Suspended';
			default:
				return status;
		}
	};

	const getTypeLabel = (type: string) => {
		switch (type) {
			case 'COURSE':
				return 'Course';
			case 'EBOOK':
				return 'E-book';
			case 'MENTORING':
				return 'Mentoring';
			case 'SUBSCRIPTION':
				return 'Subscription';
			case 'BUNDLE':
				return 'Bundle';
			default:
				return type;
		}
	};

	const getTypeIcon = (type: string) => {
		switch (type) {
			case 'COURSE':
				return GraduationCapIcon;
			case 'EBOOK':
				return BookOpenIcon;
			case 'MENTORING':
				return UsersIcon;
			case 'SUBSCRIPTION':
				return PlayCircleIcon;
			case 'BUNDLE':
				return PackageIcon;
			default:
				return ImageIcon;
		}
	};

	const formatCurrency = (cents: number, currency: string = 'BRL') => {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency,
		}).format(cents / 100);
	};

	const formatDate = (date: string | Date) => {
		return formatDistanceToNow(new Date(date), {
			addSuffix: true
		});
	};

	const handleDeleteProduct = async (productId: string) => {
		if (confirm("Are you sure you want to delete this product?")) {
			await deleteProduct.mutateAsync(productId);
		}
	};

	const handleUpdateStatus = async (productId: string, status: Product["status"]) => {
		await updateStatus.mutateAsync({ id: productId, status });
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold">My Products</h1>
						<p className="text-muted-foreground">
							Manage your digital products and track sales
						</p>
					</div>
				</div>
				<div className="grid gap-4 md:grid-cols-4">
					{[...Array(4)].map((_, i) => (
						<Card key={i}>
							<CardHeader className="animate-pulse">
								<div className="h-4 bg-gray-200 rounded w-3/4"></div>
							</CardHeader>
							<CardContent className="animate-pulse">
								<div className="h-8 bg-gray-200 rounded w-1/2"></div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold">My Products</h1>
						<p className="text-muted-foreground">
							Manage your digital products and track sales
						</p>
					</div>
				</div>
				<Card>
					<CardContent className="flex flex-col items-center justify-center py-12">
						<p className="text-red-500">Error loading products</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Calcular estatísticas
	const totalProducts = pagination?.total || 0;
	const totalEnrollments = products.reduce((acc, product) => acc + (product._count?.enrollments || 0), 0);
	const totalSales = products.reduce((acc, product) => acc + (product._count?.orders || 0), 0);
	const totalRevenue = products.reduce((acc, product) => {
		// Aqui você precisaria buscar os dados de receita de cada produto
		// Por enquanto, vamos usar um valor estimado baseado nas vendas
		return acc + ((product._count?.orders || 0) * product.priceCents);
	}, 0);

	// Filter content component
	const FilterContent = () => (
		<div className="space-y-6">
			<div className="space-y-2">
				<Label htmlFor="status-filter">Status</Label>
				<Select value={statusFilter || ""} onValueChange={(value) => setStatusFilter(value || undefined)}>
					<SelectTrigger>
						<SelectValue placeholder="All statuses" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="">All statuses</SelectItem>
						<SelectItem value="PUBLISHED">Published</SelectItem>
						<SelectItem value="DRAFT">Draft</SelectItem>
						<SelectItem value="ARCHIVED">Archived</SelectItem>
						<SelectItem value="SUSPENDED">Suspended</SelectItem>
					</SelectContent>
				</Select>
			</div>

			<div className="space-y-2">
				<Label htmlFor="type-filter">Type</Label>
				<Select value={typeFilter || ""} onValueChange={(value) => setTypeFilter(value || undefined)}>
					<SelectTrigger>
						<SelectValue placeholder="All types" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="">All types</SelectItem>
						<SelectItem value="COURSE">Course</SelectItem>
						<SelectItem value="EBOOK">E-book</SelectItem>
						<SelectItem value="MENTORING">Mentoring</SelectItem>
						<SelectItem value="SUBSCRIPTION">Subscription</SelectItem>
						<SelectItem value="BUNDLE">Bundle</SelectItem>
					</SelectContent>
				</Select>
			</div>

			<Separator />

			<div className="flex gap-2">
				<Button
					variant="outline"
					size="sm"
					onClick={() => {
						setStatusFilter(undefined);
						setTypeFilter(undefined);
					}}
				>
					Clear Filters
				</Button>
			</div>
		</div>
	);

	return (
		<PageLayout
			title="My Products"
			description="Manage your digital products and track sales"
			breadcrumbs={[
				{ label: "Dashboard", href: "/app" },
				{ label: "Products" }
			]}
			primaryAction={
				<SimpleCreateProductSheet
					organizationId={organizationId}
					organizationSlug={organizationSlug}
				/>
			}
			secondaryActions={[
				<Button
					key="view-toggle"
					variant="outline"
					size="sm"
					onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
				>
					{viewMode === "grid" ? <ListIcon className="h-4 w-4 mr-2" /> : <Grid3X3Icon className="h-4 w-4 mr-2" />}
					{viewMode === "grid" ? "List" : "Grid"}
				</Button>,
				<Button key="analytics" variant="outline" size="sm" asChild>
					<Link href="/app/analytics">
						<BarChart3Icon className="h-4 w-4 mr-2" />
						Analytics
					</Link>
				</Button>
			]}
			showSearch={true}
			searchPlaceholder="Search products..."
			searchValue={search}
			onSearchChange={setSearch}
			showFilters={true}
			filterContent={<FilterContent />}
		>
			<div className="space-y-6">
				{/* Enhanced Stats Cards */}
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<Card className="relative overflow-hidden shadow-sm border-border/50 hover:shadow-md transition-all duration-200">
						<div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/20" />
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
							<CardTitle className="text-sm font-medium text-muted-foreground">Total Products</CardTitle>
							<div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
								<ImageIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
							</div>
						</CardHeader>
						<CardContent className="relative">
							<div className="text-2xl font-bold text-foreground">{totalProducts}</div>
							<p className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
								<TrendingUpIcon className="h-3 w-3" />
								products registered
							</p>
						</CardContent>
					</Card>

					<Card className="relative overflow-hidden shadow-sm border-border/50 hover:shadow-md transition-all duration-200">
						<div className="absolute inset-0 bg-gradient-to-br from-green-50/50 to-transparent dark:from-green-950/20" />
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
							<CardTitle className="text-sm font-medium text-muted-foreground">Students</CardTitle>
							<div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30">
								<UsersIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
							</div>
						</CardHeader>
						<CardContent className="relative">
							<div className="text-2xl font-bold text-foreground">{totalEnrollments}</div>
							<p className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
								<TrendingUpIcon className="h-3 w-3" />
								enrolled students
							</p>
						</CardContent>
					</Card>

					<Card className="relative overflow-hidden shadow-sm border-border/50 hover:shadow-md transition-all duration-200">
						<div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-transparent dark:from-purple-950/20" />
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
							<CardTitle className="text-sm font-medium text-muted-foreground">Sales</CardTitle>
							<div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30">
								<DollarSignIcon className="h-4 w-4 text-purple-600 dark:text-purple-400" />
							</div>
						</CardHeader>
						<CardContent className="relative">
							<div className="text-2xl font-bold text-foreground">{totalSales}</div>
							<p className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
								<TrendingUpIcon className="h-3 w-3" />
								completed sales
							</p>
						</CardContent>
					</Card>

					<Card className="relative overflow-hidden shadow-sm border-border/50 hover:shadow-md transition-all duration-200">
						<div className="absolute inset-0 bg-gradient-to-br from-orange-50/50 to-transparent dark:from-orange-950/20" />
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
							<CardTitle className="text-sm font-medium text-muted-foreground">Total Revenue</CardTitle>
							<div className="p-2 rounded-full bg-orange-100 dark:bg-orange-900/30">
								<DollarSignIcon className="h-4 w-4 text-orange-600 dark:text-orange-400" />
							</div>
						</CardHeader>
						<CardContent className="relative">
							<div className="text-2xl font-bold text-foreground">
								{formatCurrency(totalRevenue)}
							</div>
							<p className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
								<TrendingUpIcon className="h-3 w-3" />
								accumulated revenue
							</p>
						</CardContent>
					</Card>
			</div>

			{/* Enhanced Products Grid/List */}
			<div className={viewMode === "grid" ? "grid gap-6 md:grid-cols-2 lg:grid-cols-3" : "space-y-4"}>
				{products.map((product) => {
					const TypeIcon = getTypeIcon(product.type);
					return (
						<Card key={product.id} className="group relative overflow-hidden hover:shadow-xl transition-all duration-300 border-border/50 shadow-sm hover:-translate-y-1 hover:border-primary/20 bg-card/50 backdrop-blur-sm">
							{/* Gradient overlay */}
							<div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

							{viewMode === "grid" ? (
								<>
									{/* Grid View */}
									<div className="relative">
										{/* Product Image/Thumbnail */}
										<div className="relative h-48 overflow-hidden bg-gradient-to-br from-muted/50 to-muted">
											{product.thumbnail ? (
												<img
													src={product.thumbnail}
													alt={product.name}
													className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
												/>
											) : (
												<div className="w-full h-full bg-gradient-to-br from-primary/10 via-primary/5 to-transparent flex items-center justify-center">
													<TypeIcon className="h-16 w-16 text-primary/40" />
												</div>
											)}

											{/* Status Badge */}
											<div className="absolute top-3 left-3">
												<Badge
													variant={getStatusBadgeVariant(product.status)}
													className="text-xs font-medium shadow-sm backdrop-blur-sm bg-white/95 dark:bg-black/95 border-0"
												>
													{getStatusLabel(product.status)}
												</Badge>
											</div>

											{/* Type Badge */}
											<div className="absolute top-3 right-3">
												<Badge variant="outline" className="text-xs font-medium shadow-sm backdrop-blur-sm bg-white/95 dark:bg-black/95 border-white/20">
													<TypeIcon className="h-3 w-3 mr-1" />
													{getTypeLabel(product.type)}
												</Badge>
											</div>

											{/* Quick Actions */}
											<div className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
												<DropdownMenu>
													<DropdownMenuTrigger asChild>
														<Button
															variant="secondary"
															size="sm"
															className="h-8 w-8 p-0 shadow-lg backdrop-blur-sm bg-white/90 dark:bg-black/90 hover:bg-white dark:hover:bg-black"
														>
															<MoreHorizontalIcon className="h-4 w-4" />
														</Button>
													</DropdownMenuTrigger>
													<DropdownMenuContent align="end" className="w-48">
														<DropdownMenuItem asChild>
															<Link href={organizationSlug ? `/app/${organizationSlug}/products/${product.id}` : `/app/products/${product.id}`} className="flex items-center">
																<EyeIcon className="mr-2 h-4 w-4" />
																View Product
															</Link>
														</DropdownMenuItem>
														<DropdownMenuItem asChild>
															<Link href={organizationSlug ? `/app/${organizationSlug}/products/${product.id}/settings` : `/app/products/${product.id}/settings`} className="flex items-center">
																<Settings2Icon className="mr-2 h-4 w-4" />
																Settings
															</Link>
														</DropdownMenuItem>
														<DropdownMenuSeparator />
														<DropdownMenuItem>
															<CopyIcon className="mr-2 h-4 w-4" />
															Duplicate
														</DropdownMenuItem>
														<DropdownMenuItem>
															<BarChart3Icon className="mr-2 h-4 w-4" />
															Analytics
														</DropdownMenuItem>
														<DropdownMenuSeparator />
														{product.status === 'PUBLISHED' ? (
															<DropdownMenuItem onClick={() => handleUpdateStatus(product.id, 'DRAFT')}>
																<ArchiveIcon className="mr-2 h-4 w-4" />
																Unpublish
															</DropdownMenuItem>
														) : (
															<DropdownMenuItem onClick={() => handleUpdateStatus(product.id, 'PUBLISHED')}>
																<PlayCircleIcon className="mr-2 h-4 w-4" />
																Publish
															</DropdownMenuItem>
														)}
														<DropdownMenuItem
															className="text-destructive focus:text-destructive"
															onClick={() => handleDeleteProduct(product.id)}
														>
															<TrashIcon className="mr-2 h-4 w-4" />
															Delete
														</DropdownMenuItem>
													</DropdownMenuContent>
												</DropdownMenu>
											</div>
										</div>

										{/* Product Info */}
										<CardHeader className="pb-3">
											<div className="space-y-2">
												<CardTitle className="text-lg font-bold text-foreground group-hover:text-primary transition-colors duration-200 line-clamp-2">
													{product.name}
												</CardTitle>
												<CardDescription className="text-sm text-muted-foreground line-clamp-2">
													{product.description || "No description available"}
												</CardDescription>
											</div>

											{/* Price */}
											<div className="flex items-center justify-between pt-2">
												<Badge variant="outline" className="text-base font-bold px-3 py-1 bg-primary/5 border-primary/20 text-primary">
													{formatCurrency(product.priceCents, product.currency)}
												</Badge>
												{product.category && (
													<span className="text-xs text-muted-foreground">
														{product.category.name}
													</span>
												)}
											</div>
										</CardHeader>

										{/* Metrics */}
										<CardContent className="pt-0 pb-4">
											<div className="grid grid-cols-3 gap-3">
												<div className="text-center p-2 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200">
													<div className="flex items-center justify-center mb-1">
														<UsersIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
													</div>
													<p className="text-sm font-semibold text-foreground">
														{product._count?.enrollments || 0}
													</p>
													<p className="text-xs text-muted-foreground">Students</p>
												</div>
												<div className="text-center p-2 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200">
													<div className="flex items-center justify-center mb-1">
														<DollarSignIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
													</div>
													<p className="text-sm font-semibold text-foreground">
														{product._count?.orders || 0}
													</p>
													<p className="text-xs text-muted-foreground">Sales</p>
												</div>
												<div className="text-center p-2 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200">
													<div className="flex items-center justify-center mb-1">
														<StarIcon className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
													</div>
													<p className="text-sm font-semibold text-foreground">4.8</p>
													<p className="text-xs text-muted-foreground">Rating</p>
												</div>
											</div>

											{/* Last Updated */}
											<div className="flex items-center justify-between mt-3 pt-3 border-t border-border/50">
												<div className="flex items-center gap-1 text-xs text-muted-foreground">
													<CalendarIcon className="h-3 w-3" />
													Updated {formatDate(product.updatedAt)}
												</div>
												<Button
													variant="ghost"
													size="sm"
													className="h-7 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200"
													asChild
												>
													<Link href={organizationSlug ? `/app/${organizationSlug}/products/${product.id}` : `/app/products/${product.id}`}>
														View Details
													</Link>
												</Button>
											</div>
										</CardContent>
									</div>
								</>
							) : (
								<>
									{/* List View */}
									<CardHeader className="pb-4">
										<div className="flex items-center justify-between">
											<div className="flex items-center space-x-4">
												{/* Thumbnail */}
												<div className="relative w-16 h-16 overflow-hidden rounded-lg shadow-sm">
													{product.thumbnail ? (
														<img
															src={product.thumbnail}
															alt={product.name}
															className="w-full h-full object-cover"
														/>
													) : (
														<div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center">
															<TypeIcon className="h-8 w-8 text-primary/60" />
														</div>
													)}
												</div>

												{/* Product Info */}
												<div className="flex-1 min-w-0">
													<div className="flex items-center gap-2 mb-1">
														<CardTitle className="text-lg font-bold text-foreground truncate">
															{product.name}
														</CardTitle>
														<Badge variant="outline" className="text-xs">
															<TypeIcon className="h-3 w-3 mr-1" />
															{getTypeLabel(product.type)}
														</Badge>
													</div>
													<CardDescription className="text-sm text-muted-foreground line-clamp-1">
														{product.description || "No description available"}
													</CardDescription>
													<div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
														<span className="flex items-center gap-1">
															<UsersIcon className="h-3 w-3" />
															{product._count?.enrollments || 0} students
														</span>
														<span className="flex items-center gap-1">
															<DollarSignIcon className="h-3 w-3" />
															{product._count?.orders || 0} sales
														</span>
														<span className="flex items-center gap-1">
															<CalendarIcon className="h-3 w-3" />
															{formatDate(product.updatedAt)}
														</span>
													</div>
												</div>
											</div>

											{/* Right Side Actions */}
											<div className="flex items-center gap-3">
												<Badge variant={getStatusBadgeVariant(product.status)} className="font-medium">
													{getStatusLabel(product.status)}
												</Badge>
												<Badge variant="outline" className="font-bold text-primary border-primary/20 px-3 py-1">
													{formatCurrency(product.priceCents, product.currency)}
												</Badge>
												<DropdownMenu>
													<DropdownMenuTrigger asChild>
														<Button variant="outline" size="sm" className="h-8 w-8 p-0">
															<MoreHorizontalIcon className="h-4 w-4" />
														</Button>
													</DropdownMenuTrigger>
													<DropdownMenuContent align="end" className="w-48">
														<DropdownMenuItem asChild>
															<Link href={organizationSlug ? `/app/${organizationSlug}/products/${product.id}` : `/app/products/${product.id}`} className="flex items-center">
																<EyeIcon className="mr-2 h-4 w-4" />
																View Product
															</Link>
														</DropdownMenuItem>
														<DropdownMenuItem asChild>
															<Link href={organizationSlug ? `/app/${organizationSlug}/products/${product.id}/settings` : `/app/products/${product.id}/settings`} className="flex items-center">
																<Settings2Icon className="mr-2 h-4 w-4" />
																Settings
															</Link>
														</DropdownMenuItem>
														<DropdownMenuSeparator />
														<DropdownMenuItem>
															<CopyIcon className="mr-2 h-4 w-4" />
															Duplicate
														</DropdownMenuItem>
														<DropdownMenuItem>
															<BarChart3Icon className="mr-2 h-4 w-4" />
															Analytics
														</DropdownMenuItem>
														<DropdownMenuSeparator />
														{product.status === 'PUBLISHED' ? (
															<DropdownMenuItem onClick={() => handleUpdateStatus(product.id, 'DRAFT')}>
																<ArchiveIcon className="mr-2 h-4 w-4" />
																Unpublish
															</DropdownMenuItem>
														) : (
															<DropdownMenuItem onClick={() => handleUpdateStatus(product.id, 'PUBLISHED')}>
																<PlayCircleIcon className="mr-2 h-4 w-4" />
																Publish
															</DropdownMenuItem>
														)}
														<DropdownMenuItem
															className="text-destructive focus:text-destructive"
															onClick={() => handleDeleteProduct(product.id)}
														>
															<TrashIcon className="mr-2 h-4 w-4" />
															Delete
														</DropdownMenuItem>
													</DropdownMenuContent>
												</DropdownMenu>
											</div>
										</div>
									</CardHeader>
								</>
							)}
						</Card>
					);
				})}
			</div>

			{/* Enhanced Pagination */}
			{pagination && pagination.pages > 1 && (
				<div className="flex items-center justify-center space-x-2">
					<Button
						variant="outline"
						onClick={() => setPage(page - 1)}
						disabled={page === 1}
						className="hover:bg-primary/10"
					>
						Previous
					</Button>
					<div className="flex items-center space-x-1">
						{Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
							const pageNum = i + 1;
							return (
								<Button
									key={pageNum}
									variant={page === pageNum ? "default" : "outline"}
									size="sm"
									onClick={() => setPage(pageNum)}
									className={page === pageNum ? "" : "hover:bg-primary/10"}
								>
									{pageNum}
								</Button>
							);
						})}
						{pagination.pages > 5 && (
							<>
								<span className="text-muted-foreground">...</span>
								<Button
									variant={page === pagination.pages ? "default" : "outline"}
									size="sm"
									onClick={() => setPage(pagination.pages)}
									className={page === pagination.pages ? "" : "hover:bg-primary/10"}
								>
									{pagination.pages}
								</Button>
							</>
						)}
					</div>
					<Button
						variant="outline"
						onClick={() => setPage(page + 1)}
						disabled={page === pagination.pages}
						className="hover:bg-primary/10"
					>
						Next
					</Button>
				</div>
			)}

			{products.length === 0 && !isLoading && (
				<Card className="shadow-sm border-border/50 bg-gradient-to-br from-muted/20 to-transparent">
					<CardContent className="flex flex-col items-center justify-center py-20">
						<div className="relative mb-8">
							<div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center mb-4">
								<ImageIcon className="h-10 w-10 text-primary/60" />
							</div>
							<div className="absolute -top-1 -right-1 w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
								<PlusIcon className="h-3 w-3 text-primary" />
							</div>
						</div>
						<h3 className="text-2xl font-bold mb-3 text-foreground">
							No products found
						</h3>
						<p className="text-muted-foreground text-center mb-8 max-w-md leading-relaxed">
							Create your first digital product and start selling online. It's quick and easy to get started!
						</p>
						<div className="flex flex-col sm:flex-row gap-3">
							<SimpleCreateProductSheet
								organizationId={organizationId}
								organizationSlug={organizationSlug}
								trigger={
									<Button size="lg" className="shadow-sm hover:shadow-md transition-shadow">
										<PlusIcon className="h-4 w-4 mr-2" />
										Create First Product
									</Button>
								}
							/>
							<Button variant="outline" size="lg" asChild>
								<Link href="/help/getting-started">
									<BookOpenIcon className="h-4 w-4 mr-2" />
									Learn More
								</Link>
							</Button>
						</div>
					</CardContent>
				</Card>
			)}

			{error && (
				<Card className="shadow-sm border-destructive/50 bg-gradient-to-br from-destructive/5 to-transparent">
					<CardContent className="flex flex-col items-center justify-center py-16">
						<div className="w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center mb-6">
							<ImageIcon className="h-8 w-8 text-destructive" />
						</div>
						<h3 className="text-xl font-semibold mb-2 text-destructive">
							Error loading products
						</h3>
						<p className="text-muted-foreground text-center mb-6 max-w-md">
							An error occurred while loading your products. Please try again.
						</p>
						<Button
							variant="outline"
							onClick={() => window.location.reload()}
							className="border-destructive/50 text-destructive hover:bg-destructive/10"
						>
							Try Again
						</Button>
					</CardContent>
				</Card>
			)}

			{isLoading && (
				<div className={viewMode === "grid" ? "grid gap-6 md:grid-cols-2 lg:grid-cols-3" : "space-y-4"}>
					{[...Array(6)].map((_, i) => (
						<Card key={i} className="shadow-sm border-border/50 animate-pulse">
							{viewMode === "grid" ? (
								<>
									<div className="h-48 bg-muted rounded-t-lg"></div>
									<CardHeader className="pb-4">
										<div className="space-y-3">
											<div className="h-5 bg-muted rounded w-3/4"></div>
											<div className="h-4 bg-muted rounded w-full"></div>
											<div className="h-4 bg-muted rounded w-2/3"></div>
										</div>
									</CardHeader>
									<CardContent>
										<div className="grid grid-cols-3 gap-3">
											{[...Array(3)].map((_, j) => (
												<div key={j} className="text-center p-2 rounded-lg bg-muted/50">
													<div className="h-4 bg-muted rounded mb-1"></div>
													<div className="h-3 bg-muted rounded w-2/3 mx-auto"></div>
												</div>
											))}
										</div>
									</CardContent>
								</>
							) : (
								<CardHeader className="pb-4">
									<div className="flex items-center space-x-4">
										<div className="w-16 h-16 bg-muted rounded-lg"></div>
										<div className="space-y-2 flex-1">
											<div className="h-5 bg-muted rounded w-3/4"></div>
											<div className="h-4 bg-muted rounded w-full"></div>
											<div className="h-3 bg-muted rounded w-1/2"></div>
										</div>
										<div className="space-y-2">
											<div className="h-6 bg-muted rounded w-20"></div>
											<div className="h-6 bg-muted rounded w-16"></div>
										</div>
									</div>
								</CardHeader>
							)}
						</Card>
					))}
				</div>
			)}
			</div>
		</PageLayout>
	);
}
