import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { ProductConfigurationClient } from "./components/ProductConfigurationClient";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon, EyeIcon } from "lucide-react";
import Link from "next/link";

interface PageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function ProductConfigurationPage({ params }: PageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  // Buscar produto e verificar se o usuário tem acesso
  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
      _count: {
        select: {
          orders: true,
          enrollments: true,
        },
      },
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="flex-1 overflow-auto p-6">
      <ProductConfigurationClient
        product={product}
        organizationSlug={organizationSlug}
      />
    </div>
  );
}
