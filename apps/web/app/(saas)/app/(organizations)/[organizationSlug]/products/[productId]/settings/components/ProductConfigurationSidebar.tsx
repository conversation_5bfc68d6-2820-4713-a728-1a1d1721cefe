"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@ui/lib";

interface ProductConfigurationSidebarProps {
  organizationSlug: string;
  productId: string;
}

const sidebarItems = [
  {
    id: "offers",
    label: "Offers",
    href: "offers",
  },
  {
    id: "checkouts",
    label: "Checkouts",
    href: "checkouts",
  },
  {
    id: "settings",
    label: "Settings",
    href: "settings",
  },
  {
    id: "tracking",
    label: "Tracking Pixels",
    href: "tracking",
  },
  {
    id: "upsells",
    label: "Upsells & More",
    href: "upsells",
  },
  {
    id: "coupons",
    label: "Coupons",
    href: "coupons",
  },
  {
    id: "affiliates",
    label: "Affiliates",
    href: "affiliates",
  },
  {
    id: "partnerships",
    label: "Partnerships",
    href: "partnerships",
  },
];

export function ProductConfigurationSidebar({
  organizationSlug,
  productId
}: ProductConfigurationSidebarProps) {
  const pathname = usePathname();
  const currentSection = pathname.split('/').pop() || 'configuracoes';

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="px-6 py-6 border-b border-border/50">
        <h2 className="text-lg font-semibold text-foreground">Configurações</h2>
        <p className="text-sm text-muted-foreground mt-1">Gerencie seu produto</p>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        {sidebarItems.map((item) => {
          const isActive = currentSection === item.href;

          return (
            <Link
              key={item.id}
              href={`/app/${organizationSlug}/products/${productId}/${item.href}`}
              className={cn(
                "block px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200",
                isActive
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
              )}
            >
              {item.label}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-border/50">
        <div className="text-xs text-muted-foreground text-center">
          <p>Configurações | set</p>
        </div>
      </div>
    </div>
  );
}
