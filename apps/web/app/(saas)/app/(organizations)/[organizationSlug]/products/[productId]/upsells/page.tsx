import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { PlusIcon, TrendingUpIcon } from "lucide-react";

interface PageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function UpsellsPage({ params }: PageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      <div className="border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Products</span>
            <span>/</span>
            <span>{product.name.length > 20 ? `${product.name.substring(0, 20)}...` : product.name}</span>
            <span>/</span>
            <span className="text-foreground font-medium">Upsells & More</span>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Upsells & More</h1>
              <p className="text-muted-foreground">Configure complementary offers to increase average ticket</p>
            </div>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              New Offer
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Complementary Offers</CardTitle>
              <CardDescription>
                Configure upsells, downsells and bonus offers to maximize your sales
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUpIcon className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">No complementary offers configured</h3>
                <p className="text-muted-foreground mb-4">
                  Configure upsell and downsell offers to increase your revenue
                </p>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Available features:
                  </p>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Upsell offers (complementary products)</li>
                    <li>• Downsell offers (cheaper alternatives)</li>
                    <li>• Automatic offer sequence</li>
                    <li>• Conversion reports</li>
                  </ul>
                  <div className="pt-4 flex justify-center">
                    <Button disabled>
                      <PlusIcon className="h-4 w-4 mr-2" />
                      In Development
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
