import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database/prisma/client";
import { redirect } from "next/navigation";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { Button } from "@ui/components/button";
import { ArrowLeftIcon, EditIcon, SettingsIcon, ExternalLinkIcon, TrendingUpIcon, UsersIcon, DollarSignIcon } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

interface ProductPageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  // Buscar produto e verificar se o usuário tem acesso
  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
      _count: {
        select: {
          orders: true,
          enrollments: true,
        },
      },
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  const formatCurrency = (cents: number, currency: string = 'BRL') => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency,
    }).format(cents / 100);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title={product.name}
        subtitle={product.description || "Visualize e gerencie seu produto"}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={`/app/${organizationSlug}/products/${productId}/configuracoes`}>
                <SettingsIcon className="h-4 w-4 mr-2" />
                Configurações
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/app/${organizationSlug}/products/${productId}/edit`}>
                <EditIcon className="h-4 w-4 mr-2" />
                Editar
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/app/${organizationSlug}/products`}>
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Voltar
              </Link>
            </Button>
          </div>
        }
      />

      {/* Card Principal do Produto */}
      <div className="bg-card border rounded-xl overflow-hidden hover:shadow-lg transition-all duration-200 group cursor-pointer">
        <Link href={`/app/${organizationSlug}/products/${productId}/ofertas`} className="block">
          <div className="flex flex-col md:flex-row">
            {/* Imagem do Produto */}
            <div className="relative w-full md:w-80 h-48 md:h-64 bg-muted flex-shrink-0">
              {product.thumbnail ? (
                <Image
                  src={product.thumbnail}
                  alt={product.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-200"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary/10 to-primary/5">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-2">
                      <TrendingUpIcon className="w-8 h-8 text-primary" />
                    </div>
                    <p className="text-sm text-muted-foreground">Sem imagem</p>
                  </div>
                </div>
              )}
              {/* Badge de Status */}
              <div className="absolute top-3 left-3">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  product.status === 'PUBLISHED'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                    : product.status === 'DRAFT'
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                }`}>
                  {product.status}
                </span>
              </div>
            </div>

            {/* Conteúdo do Card */}
            <div className="flex-1 p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h2 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors">
                    {product.name}
                  </h2>
                  <p className="text-sm text-muted-foreground mt-1">
                    {product.type} • {product.category?.name || 'Sem categoria'}
                  </p>
                </div>
                <ExternalLinkIcon className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
              </div>

              {/* Descrição */}
              {product.shortDescription && (
                <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                  {product.shortDescription}
                </p>
              )}

              {/* Estatísticas */}
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full mx-auto mb-1">
                    <DollarSignIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <p className="text-xs text-muted-foreground">Vendas</p>
                  <p className="text-sm font-semibold">{product._count?.orders || 0}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full mx-auto mb-1">
                    <UsersIcon className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <p className="text-xs text-muted-foreground">Alunos</p>
                  <p className="text-sm font-semibold">{product._count?.enrollments || 0}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-full mx-auto mb-1">
                    <TrendingUpIcon className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  </div>
                  <p className="text-xs text-muted-foreground">Receita</p>
                  <p className="text-sm font-semibold">
                    {formatCurrency((product._count?.orders || 0) * product.priceCents, product.currency)}
                  </p>
                </div>
              </div>

              {/* Preço */}
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-foreground">
                    {formatCurrency(product.priceCents, product.currency)}
                  </p>
                  {product.comparePriceCents && (
                    <p className="text-sm text-muted-foreground line-through">
                      {formatCurrency(product.comparePriceCents, product.currency)}
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <p className="text-xs text-muted-foreground">Clique para ver ofertas</p>
                  <p className="text-xs text-primary font-medium">Gerenciar →</p>
                </div>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* Ações Rápidas */}
      <div className="grid gap-4 md:grid-cols-3">
        <Button variant="outline" className="h-auto p-4 flex flex-col items-start" asChild>
          <Link href={`/app/${organizationSlug}/products/${productId}/ofertas`}>
            <div className="flex items-center gap-2 mb-2">
              <TrendingUpIcon className="w-4 h-4" />
              <span className="font-medium">Gerenciar Ofertas</span>
            </div>
            <p className="text-xs text-muted-foreground text-left">Criar e gerenciar ofertas especiais</p>
          </Link>
        </Button>

        <Button variant="outline" className="h-auto p-4 flex flex-col items-start" asChild>
          <Link href={`/app/${organizationSlug}/products/${productId}/checkouts`}>
            <div className="flex items-center gap-2 mb-2">
              <SettingsIcon className="w-4 h-4" />
              <span className="font-medium">Configurar Checkouts</span>
            </div>
            <p className="text-xs text-muted-foreground text-left">Personalizar processo de compra</p>
          </Link>
        </Button>

        <Button variant="outline" className="h-auto p-4 flex flex-col items-start" asChild>
          <Link href={`/app/${organizationSlug}/products/${productId}/analytics`}>
            <div className="flex items-center gap-2 mb-2">
              <DollarSignIcon className="w-4 h-4" />
              <span className="font-medium">Ver Analytics</span>
            </div>
            <p className="text-xs text-muted-foreground text-left">Acompanhar performance e vendas</p>
          </Link>
        </Button>
      </div>

      {/* Descrição */}
      {product.description && (
        <div className="bg-card border rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Descrição</h3>
          <p className="text-muted-foreground">
            {product.description}
          </p>
        </div>
      )}
    </div>
  );
}
