import { getActiveOrganization } from "@saas/auth/lib/server";
import { getSession } from "@saas/auth/lib/server";
import { ProductsClient } from "../../../products/components/products-client";
import { notFound } from "next/navigation";

interface ProductsPageProps {
  params: Promise<{ organizationSlug: string }>;
}

export default async function ProductsPage({ params }: ProductsPageProps) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);
  const session = await getSession();

  if (!organization) {
    return notFound();
  }

  if (!session) {
    return notFound();
  }

  return <ProductsClient organizationId={organization.id} userId={session.user.id} organizationSlug={organizationSlug} />;
}
