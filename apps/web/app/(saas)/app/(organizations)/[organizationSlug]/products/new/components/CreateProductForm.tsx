"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { useCreateProduct, CreateProductData } from "@saas/products/hooks/useProductsApi";
import { Loader2, Upload, X } from "lucide-react";

const createProductSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório").max(60, "Nome deve ter no máximo 60 caracteres"),
  slug: z.string().min(1, "Slug é obrigatório").regex(/^[a-z0-9-]+$/, "Slug deve conter apenas letras minúsculas, números e hífens"),
  description: z.string().max(200, "Descrição deve ter no máximo 200 caracteres").optional(),
  shortDescription: z.string().max(100, "Descrição curta deve ter no máximo 100 caracteres").optional(),
  priceCents: z.number().min(100, "Preço deve ser maior que R$ 1,00"),
  comparePriceCents: z.number().min(0).optional(),
  currency: z.string().default("BRL"),
  type: z.enum(["COURSE", "EBOOK", "MENTORSHIP", "SUBSCRIPTION", "BUNDLE"]),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED", "SUSPENDED"]).default("DRAFT"),
  visibility: z.enum(["PUBLIC", "PRIVATE", "UNLISTED"]).default("PRIVATE"),
  categoryId: z.string().optional(),
  thumbnail: z.string().optional(),
  tags: z.array(z.string()).default([]),
  features: z.array(z.string()).default([]),
  requirements: z.array(z.string()).default([]),
  duration: z.number().min(0).optional(),
  level: z.string().optional(),
  language: z.string().default("pt-BR"),
  certificate: z.boolean().default(false),
  downloadable: z.boolean().default(false),
  checkoutType: z.enum(["DEFAULT", "CUSTOM", "EXTERNAL"]).default("DEFAULT"),
  settings: z.record(z.any()).default({}),
});

type CreateProductFormData = z.infer<typeof createProductSchema>;

interface CreateProductFormProps {
  organization: any;
  organizationSlug: string;
}

export function CreateProductForm({ organization, organizationSlug }: CreateProductFormProps) {
  const router = useRouter();
  const [isUploading, setIsUploading] = useState(false);
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);
  
  const createProduct = useCreateProduct();

  const form = useForm<CreateProductFormData>({
    resolver: zodResolver(createProductSchema),
    defaultValues: {
      currency: "BRL",
      status: "DRAFT",
      visibility: "PRIVATE",
      type: "COURSE",
      language: "pt-BR",
      certificate: false,
      downloadable: false,
      checkoutType: "DEFAULT",
      tags: [],
      features: [],
      requirements: [],
      settings: {},
    },
  });

  // Auto-generate slug from name
  const watchName = form.watch("name");
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  };

  // Update slug when name changes
  useState(() => {
    if (watchName && !form.getValues("slug")) {
      form.setValue("slug", generateSlug(watchName));
    }
  });

  const handleThumbnailUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Por favor, selecione uma imagem válida");
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("A imagem deve ter no máximo 5MB");
      return;
    }

    setIsUploading(true);
    
    try {
      // Get signed upload URL
      const filename = `${organization.id}/${Date.now()}-${file.name}`;
      const response = await fetch(`/api/uploads/signed-upload-url?bucket=products&path=${filename}`, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Erro ao obter URL de upload");
      }

      const { signedUrl } = await response.json();

      // Upload file to S3
      const uploadResponse = await fetch(signedUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error("Erro ao fazer upload da imagem");
      }

      // Set thumbnail URL in form - use image proxy for consistent access
      const thumbnailUrl = `/image-proxy/products/${filename}`;
      form.setValue("thumbnail", thumbnailUrl);
      setThumbnailPreview(URL.createObjectURL(file));

      toast.success("Imagem carregada com sucesso!");
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Erro ao fazer upload da imagem");
    } finally {
      setIsUploading(false);
    }
  };

  const removeThumbnail = () => {
    form.setValue("thumbnail", "");
    setThumbnailPreview(null);
  };

  const onSubmit = async (data: CreateProductFormData) => {
    try {
      const productData: CreateProductData = {
        ...data,
        organizationId: organization.id,
      };

      const result = await createProduct.mutateAsync(productData);
      
      toast.success("Produto criado com sucesso!");
      router.push(`/app/${organizationSlug}/products/${result.product.id}`);
    } catch (error) {
      console.error("Create product error:", error);
      toast.error("Erro ao criar produto");
    }
  };

  const formatPrice = (cents: number) => {
    return (cents / 100).toLocaleString("pt-BR", {
      style: "currency",
      currency: "BRL",
    });
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
      <div className="grid gap-8 lg:grid-cols-2">
        {/* Informações Básicas */}
        <Card className="shadow-sm border-border/50">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg">Informações Básicas</CardTitle>
            <CardDescription>
              Informações principais do produto
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-5">
            <div className="space-y-3">
              <Label htmlFor="name" className="text-sm font-medium">Nome do Produto *</Label>
              <Input
                id="name"
                placeholder="Ex: Curso de Marketing Digital"
                className="h-11"
                {...form.register("name")}
              />
              {form.formState.errors.name && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <span className="w-1 h-1 bg-destructive rounded-full"></span>
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="space-y-3">
              <Label htmlFor="slug" className="text-sm font-medium">Slug *</Label>
              <Input
                id="slug"
                placeholder="curso-marketing-digital"
                className="h-11"
                {...form.register("slug")}
              />
              {form.formState.errors.slug && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <span className="w-1 h-1 bg-destructive rounded-full"></span>
                  {form.formState.errors.slug.message}
                </p>
              )}
            </div>

            <div className="space-y-3">
              <Label htmlFor="description" className="text-sm font-medium">Descrição</Label>
              <Textarea
                id="description"
                placeholder="Descreva seu produto..."
                className="min-h-[100px] resize-none"
                {...form.register("description")}
              />
              {form.formState.errors.description && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <span className="w-1 h-1 bg-destructive rounded-full"></span>
                  {form.formState.errors.description.message}
                </p>
              )}
            </div>

            <div className="space-y-3">
              <Label htmlFor="shortDescription" className="text-sm font-medium">Descrição Curta</Label>
              <Input
                id="shortDescription"
                placeholder="Resumo do produto"
                className="h-11"
                {...form.register("shortDescription")}
              />
              {form.formState.errors.shortDescription && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <span className="w-1 h-1 bg-destructive rounded-full"></span>
                  {form.formState.errors.shortDescription.message}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Preços e Configurações */}
        <Card className="shadow-sm border-border/50">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg">Preços e Configurações</CardTitle>
            <CardDescription>
              Configure preços e configurações de venda
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-5">
            <div className="space-y-3">
              <Label htmlFor="priceCents" className="text-sm font-medium">Preço (em centavos) *</Label>
              <Input
                id="priceCents"
                type="number"
                placeholder="Ex: 29700 (R$ 297,00)"
                className="h-11"
                {...form.register("priceCents", { valueAsNumber: true })}
              />
              {form.formState.errors.priceCents && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <span className="w-1 h-1 bg-destructive rounded-full"></span>
                  {form.formState.errors.priceCents.message}
                </p>
              )}
              {form.watch("priceCents") && (
                <p className="text-sm text-muted-foreground">
                  Preço: {formatPrice(form.watch("priceCents"))}
                </p>
              )}
            </div>

            <div className="space-y-3">
              <Label htmlFor="type" className="text-sm font-medium">Tipo de Produto *</Label>
              <Select onValueChange={(value) => form.setValue("type", value as any)}>
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="COURSE">Curso</SelectItem>
                  <SelectItem value="EBOOK">E-book</SelectItem>
                  <SelectItem value="MENTORSHIP">Mentoria</SelectItem>
                  <SelectItem value="SUBSCRIPTION">Assinatura</SelectItem>
                  <SelectItem value="BUNDLE">Pacote</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.type && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <span className="w-1 h-1 bg-destructive rounded-full"></span>
                  {form.formState.errors.type.message}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Thumbnail Upload */}
      <Card className="shadow-sm border-border/50">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg">Imagem do Produto</CardTitle>
          <CardDescription>
            Adicione uma imagem para representar seu produto
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {thumbnailPreview ? (
              <div className="relative inline-block">
                <img
                  src={thumbnailPreview}
                  alt="Preview"
                  className="w-32 h-32 object-cover rounded-lg border"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                  onClick={removeThumbnail}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ) : (
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                <Upload className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground mb-2">
                  Clique para fazer upload de uma imagem
                </p>
                <p className="text-xs text-muted-foreground">
                  PNG, JPG até 5MB
                </p>
              </div>
            )}

            <div className="flex items-center gap-2">
              <Input
                type="file"
                accept="image/*"
                onChange={handleThumbnailUpload}
                disabled={isUploading}
                className="hidden"
                id="thumbnail-upload"
              />
              <Label
                htmlFor="thumbnail-upload"
                className="cursor-pointer"
              >
                <Button
                  type="button"
                  variant="outline"
                  disabled={isUploading}
                  asChild
                >
                  <span>
                    {isUploading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Carregando...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Selecionar Imagem
                      </>
                    )}
                  </span>
                </Button>
              </Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-between pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
        >
          Cancelar
        </Button>

        <Button
          type="submit"
          disabled={createProduct.isPending || isUploading}
          className="min-w-[120px]"
        >
          {createProduct.isPending ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Criando...
            </>
          ) : (
            "Criar Produto"
          )}
        </Button>
      </div>
    </form>
  );
}
