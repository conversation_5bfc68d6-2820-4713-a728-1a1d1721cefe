"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ui/components/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { Checkbox } from "@ui/components/checkbox";
import { PlusIcon, LoaderIcon } from "lucide-react";

// Schema de validação para criar pixel
const createPixelSchema = z.object({
  name: z.string()
    .min(1, "Nome é obrigatório")
    .max(100, "Nome deve ter no máximo 100 caracteres"),
  
  platform: z.enum(["facebook", "google", "tiktok", "custom"], {
    required_error: "Selecione uma plataforma",
  }),

  pixelId: z.string()
    .min(1, "ID do pixel é obrigatório")
    .max(50, "ID deve ter no máximo 50 caracteres"),

  events: z.array(z.string()).min(1, "Selecione pelo menos um evento"),

  isActive: z.boolean().default(true),

  // Configurações específicas por plataforma
  accessToken: z.string().optional(),
  testEventCode: z.string().optional(),
});

type CreatePixelFormData = z.infer<typeof createPixelSchema>;

interface CreatePixelModalProps {
  organizationSlug: string;
  productId: string;
  product: any;
  variant?: "default" | "outline";
  buttonText?: string;
}

const platformOptions = [
  { 
    value: "facebook", 
    label: "Facebook Pixel", 
    description: "Rastreamento para Facebook e Instagram Ads" 
  },
  { 
    value: "google", 
    label: "Google Analytics", 
    description: "Analytics e Google Ads" 
  },
  { 
    value: "tiktok", 
    label: "TikTok Pixel", 
    description: "Rastreamento para TikTok Ads" 
  },
  { 
    value: "custom", 
    label: "Pixel Personalizado", 
    description: "Código de rastreamento customizado" 
  },
];

const eventOptions = {
  facebook: [
    { id: "PageView", label: "Visualização de Página", description: "Quando alguém visita uma página" },
    { id: "Purchase", label: "Compra", description: "Quando uma compra é realizada" },
    { id: "AddToCart", label: "Adicionar ao Carrinho", description: "Quando um item é adicionado ao carrinho" },
    { id: "InitiateCheckout", label: "Iniciar Checkout", description: "Quando o checkout é iniciado" },
    { id: "Lead", label: "Lead", description: "Quando um lead é gerado" },
  ],
  google: [
    { id: "page_view", label: "Visualização de Página", description: "Quando alguém visita uma página" },
    { id: "purchase", label: "Compra", description: "Quando uma compra é realizada" },
    { id: "add_to_cart", label: "Adicionar ao Carrinho", description: "Quando um item é adicionado ao carrinho" },
    { id: "begin_checkout", label: "Iniciar Checkout", description: "Quando o checkout é iniciado" },
  ],
  tiktok: [
    { id: "ViewContent", label: "Visualizar Conteúdo", description: "Quando alguém visualiza conteúdo" },
    { id: "CompletePayment", label: "Completar Pagamento", description: "Quando um pagamento é completado" },
    { id: "AddToCart", label: "Adicionar ao Carrinho", description: "Quando um item é adicionado ao carrinho" },
  ],
  custom: [
    { id: "custom_event", label: "Evento Personalizado", description: "Evento customizado" },
  ],
};

export function CreatePixelModal({
  organizationSlug,
  productId,
  product,
  variant = "default",
  buttonText = "Adicionar Pixel",
}: CreatePixelModalProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<CreatePixelFormData>({
    resolver: zodResolver(createPixelSchema),
    defaultValues: {
      name: "",
      platform: "facebook",
      pixelId: "",
      events: [],
      isActive: true,
      accessToken: "",
      testEventCode: "",
    },
  });

  const watchPlatform = form.watch("platform");
  const availableEvents = eventOptions[watchPlatform] || [];

  const onSubmit = async (data: CreatePixelFormData) => {
    setIsLoading(true);
    try {
      // TODO: Implementar chamada para API
      console.log("Dados do pixel:", data);
      
      // Simular delay da API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Fechar modal e resetar form
      setOpen(false);
      form.reset();
      
      // TODO: Revalidar dados da página ou mostrar toast de sucesso
    } catch (error) {
      console.error("Erro ao criar pixel:", error);
      // TODO: Mostrar toast de erro
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={variant}>
          <PlusIcon className="h-4 w-4 mr-2" />
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Adicionar Pixel de Rastreamento</DialogTitle>
          <DialogDescription>
            Configure um pixel de rastreamento para o produto "{product.name}"
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Nome do Pixel */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome do Pixel</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Facebook Pixel Principal" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Plataforma */}
            <FormField
              control={form.control}
              name="platform"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Plataforma</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione a plataforma" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {platformOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div>
                            <div className="font-medium">{option.label}</div>
                            <div className="text-sm text-muted-foreground">{option.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* ID do Pixel */}
            <FormField
              control={form.control}
              name="pixelId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ID do Pixel</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder={
                        watchPlatform === "facebook" ? "123456789012345" :
                        watchPlatform === "google" ? "GA-XXXXXXXXX-X" :
                        watchPlatform === "tiktok" ? "XXXXXXXXXXXXXXXXX" :
                        "ID do pixel"
                      }
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Eventos */}
            <FormField
              control={form.control}
              name="events"
              render={() => (
                <FormItem>
                  <FormLabel>Eventos para Rastrear</FormLabel>
                  <div className="space-y-3">
                    {availableEvents.map((event) => (
                      <FormField
                        key={event.id}
                        control={form.control}
                        name="events"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={event.id}
                              className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(event.id)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...field.value, event.id])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== event.id
                                          )
                                        )
                                  }}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel className="text-sm font-medium">
                                  {event.label}
                                </FormLabel>
                                <p className="text-sm text-muted-foreground">
                                  {event.description}
                                </p>
                              </div>
                            </FormItem>
                          )
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Pixel Ativo */}
            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Pixel Ativo</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      O pixel estará ativo e coletando dados
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isLoading}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />}
                Adicionar Pixel
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
