import type { PropsWithChildren } from "react";
import { ProductConfigurationSidebar } from "./configuracoes/components/ProductConfigurationSidebar";

interface ProductLayoutProps extends PropsWithChildren {
  params: Promise<{ organizationSlug: string; productId: string }>;
}

export default async function ProductLayout({
  children,
  params
}: ProductLayoutProps) {
  const { organizationSlug, productId } = await params;

  return (
    // Full-screen layout that breaks out of AppWrapper constraints
    <div className="fixed inset-0 top-[72px] bg-background">
      <div className="flex h-full">
        {/* Sidebar */}
        <div className="w-64 border-r border-border/50 bg-card flex-shrink-0 flex flex-col">
          <ProductConfigurationSidebar
            organizationSlug={organizationSlug}
            productId={productId}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden bg-background">
          {children}
        </div>
      </div>
    </div>
  );
}
