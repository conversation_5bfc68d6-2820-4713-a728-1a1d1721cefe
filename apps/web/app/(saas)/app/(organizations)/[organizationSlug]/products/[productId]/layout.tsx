import type { PropsWithChildren } from "react";
import { ProductConfigurationSidebar } from "./configuracoes/components/ProductConfigurationSidebar";

interface ProductLayoutProps extends PropsWithChildren {
  params: Promise<{ organizationSlug: string; productId: string }>;
}

export default async function ProductLayout({
  children,
  params
}: ProductLayoutProps) {
  const { organizationSlug, productId } = await params;

  return (
    <div className="flex h-full min-h-[calc(100vh-200px)]  ">
      {/* Sidebar */}
      <div className="w-64 border-r border-border/50 bg-muted/20 flex-shrink-0">
        <ProductConfigurationSidebar
          organizationSlug={organizationSlug}
          productId={productId}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {children}
      </div>
    </div>
  );
}
