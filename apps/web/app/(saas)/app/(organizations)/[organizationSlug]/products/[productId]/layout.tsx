import type { PropsWithChildren } from "react";
import { ProductConfigurationSidebar } from "./settings/components/ProductConfigurationSidebar";

interface ProductLayoutProps extends PropsWithChildren {
  params: Promise<{ organizationSlug: string; productId: string }>;
}

export default async function ProductLayout({
  children,
  params
}: ProductLayoutProps) {
  const { organizationSlug, productId } = await params;

  return (
    // Layout que utiliza todo o espaço interno do AppWrapper
    <div className="-mx-4 md:-mx-8 -my-6 md:-my-8 min-h-[calc(100vh-200px)]">
      <div className="flex h-full min-h-[calc(100vh-200px)]">
        {/* Sidebar */}
        <div className="w-64 border-r border-border/50 bg-card flex-shrink-0 flex flex-col">
          <ProductConfigurationSidebar
            organizationSlug={organizationSlug}
            productId={productId}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden bg-background">
          {children}
        </div>
      </div>
    </div>
  );
}
