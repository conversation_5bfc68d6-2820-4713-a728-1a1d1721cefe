import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { PlusIcon } from "lucide-react";
import { CreateOfferModal } from "./components/CreateOfferModal";

interface PageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function OffersPage({ params }: PageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  // Buscar produto e verificar se o usuário tem acesso
  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center px-6 py-4">
          {/* Breadcrumbs */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Products</span>
            <span>/</span>
            <span>{product.name.length > 20 ? `${product.name.substring(0, 20)}...` : product.name}</span>
            <span>/</span>
            <span className="text-foreground font-medium">Offers</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Offers</h1>
              <p className="text-muted-foreground">Manage your product offers and promotions</p>
            </div>
            <CreateOfferModal
              organizationSlug={organizationSlug}
              productId={productId}
              product={product}
            />
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Active Offers</CardTitle>
              <CardDescription>
                Configure special offers to increase your sales
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <PlusIcon className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">No offers created</h3>
                <p className="text-muted-foreground mb-6">
                  Create your first offer to start selling more
                </p>
                <div className="flex justify-center">
                  <CreateOfferModal
                    organizationSlug={organizationSlug}
                    productId={productId}
                    product={product}
                    variant="default"
                    buttonText="Create First Offer"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
