import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { redirect } from "next/navigation";
import { CreateOfferModal } from "./components/CreateOfferModal";
import { OffersPageClient } from "./components/OffersPageClient";

interface PageProps {
  params: Promise<{
    organizationSlug: string;
    productId: string;
  }>;
}

export default async function OffersPage({ params }: PageProps) {
  const { organizationSlug, productId } = await params;
  const session = await getSession();

  if (!session) {
    redirect("/auth/login");
  }

  // Buscar produto e verificar se o usuário tem acesso
  const product = await db.product.findFirst({
    where: {
      id: productId,
      organization: {
        slug: organizationSlug,
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      organization: true,
      category: true,
    },
  });

  if (!product) {
    redirect(`/app/${organizationSlug}/products`);
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center px-6 py-4">
          {/* Breadcrumbs */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>Produtos</span>
            <span>/</span>
            <span>{product.name.length > 20 ? `${product.name.substring(0, 20)}...` : product.name}</span>
            <span>/</span>
            <span className="text-foreground font-medium">Ofertas</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Ofertas</h1>
              <p className="text-muted-foreground">Gerencie as ofertas e promoções do seu produto</p>
            </div>
            <CreateOfferModal
              organizationSlug={organizationSlug}
              productId={productId}
              product={product}
            />
          </div>

          <OffersPageClient
            organizationSlug={organizationSlug}
            productId={productId}
            product={product}
          />
        </div>
      </div>
    </div>
  );
}
