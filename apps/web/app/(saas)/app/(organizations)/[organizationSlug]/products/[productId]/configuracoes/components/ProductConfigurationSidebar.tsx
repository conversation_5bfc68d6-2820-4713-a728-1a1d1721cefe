"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@ui/lib";

interface ProductConfigurationSidebarProps {
  organizationSlug: string;
  productId: string;
}

const sidebarItems = [
  {
    id: "settings",
    label: "Informações Básicas",
    href: "settings",
    description: "Nome, preço, descrição e configurações"
  },
  {
    id: "checkouts",
    label: "Checkouts",
    href: "checkouts",
    description: "Configuração de pagamentos e links"
  },
  {
    id: "offers",
    label: "Ofertas",
    href: "offers",
    description: "Promoções e ofertas especiais"
  },
  {
    id: "analytics",
    label: "Analytics",
    href: "analytics",
    description: "Vendas e métricas de performance"
  },
  {
    id: "content",
    label: "Conteúdo",
    href: "content",
    description: "Upload de materiais e arquivos"
  },
  {
    id: "tracking",
    label: "Pixels de Rastreamento",
    href: "tracking",
    description: "Google Analytics e Facebook Pixel"
  },
  {
    id: "upsells",
    label: "Upsells e Mais",
    href: "upsells",
    description: "Produtos relacionados e cross-selling"
  },
  {
    id: "coupons",
    label: "Cupons",
    href: "coupons",
    description: "Descontos e códigos promocionais"
  },
  {
    id: "affiliates",
    label: "Afiliados",
    href: "affiliates",
    description: "Programa de afiliação e comissões"
  },
  {
    id: "partnerships",
    label: "Parcerias",
    href: "partnerships",
    description: "Coprodução e parcerias estratégicas"
  },
];

export function ProductConfigurationSidebar({
  organizationSlug,
  productId
}: ProductConfigurationSidebarProps) {
  const pathname = usePathname();
  const currentSection = pathname.split('/').pop() || 'configuracoes';

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="px-6 py-6 border-b border-border/50 bg-gradient-to-r from-primary/5 to-primary/10">
        <h2 className="text-lg font-semibold text-foreground">Configurações</h2>
        <p className="text-sm text-muted-foreground mt-1">Gerencie seu produto</p>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        {sidebarItems.map((item) => {
          const isActive = currentSection === item.href;

          return (
            <Link
              key={item.id}
              href={`/app/${organizationSlug}/products/${productId}/${item.href}`}
              className={cn(
                "block px-4 py-3 rounded-xl transition-all duration-200 group border-2 border-transparent",
                isActive
                  ? "bg-primary text-primary-foreground shadow-lg border-primary/30 scale-[1.02]"
                  : "text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 hover:border-muted-foreground/10 hover:scale-[1.01]"
              )}
            >
              <div className="font-semibold text-sm mb-1.5">{item.label}</div>
              <div className={cn(
                "text-xs leading-relaxed",
                isActive ? "text-primary-foreground/80" : "text-muted-foreground/70"
              )}>
                {item.description}
              </div>
            </Link>
          );
        })}
      </nav>

    </div>
  );
}
