"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@ui/lib";
import {
  PackageIcon,
  CreditCardIcon,
  SettingsIcon,
  BarChart3Icon,
  TrendingUpIcon,
  TagIcon,
  UsersIcon,
  HandshakeIcon,
} from "lucide-react";

interface ProductConfigurationSidebarProps {
  organizationSlug: string;
  productId: string;
}

const sidebarItems = [
  {
    id: "ofertas",
    label: "Ofertas",
    icon: PackageIcon,
    href: "ofertas",
  },
  {
    id: "checkouts",
    label: "Checkouts",
    icon: CreditCardIcon,
    href: "checkouts",
  },
  {
    id: "configuracoes",
    label: "Configurações",
    icon: SettingsIcon,
    href: "configuracoes",
  },
  {
    id: "pixels",
    label: "Pixels de rastreamento",
    icon: BarChart3Icon,
    href: "pixels",
  },
  {
    id: "upsell",
    label: "Upsell, downsell e mais",
    icon: TrendingUpIcon,
    href: "upsell",
  },
  {
    id: "cupons",
    label: "Cupons",
    icon: TagIcon,
    href: "cupons",
  },
  {
    id: "afiliacao",
    label: "Afiliação",
    icon: UsersIcon,
    href: "afiliacao",
  },
  {
    id: "coproducao",
    label: "Coprodução",
    icon: HandshakeIcon,
    href: "coproducao",
  },
];

export function ProductConfigurationSidebar({
  organizationSlug,
  productId
}: ProductConfigurationSidebarProps) {
  const pathname = usePathname();
  const currentSection = pathname.split('/').pop() || 'configuracoes';

  return (
    <div className="h-full flex flex-col  ">
      {/* Header */}
      <div className="p-6 border-b border-border/50">
        <h2 className="text-lg font-semibold text-foreground">Configurações</h2>
        <p className="text-sm text-muted-foreground mt-1">Gerencie seu produto</p>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {sidebarItems.map((item) => {
          const Icon = item.icon;
          const isActive = currentSection === item.href;

          return (
            <Link
              key={item.id}
              href={`/app/${organizationSlug}/products/${productId}/${item.href}`}
              className={cn(
                "flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200",
                isActive
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
              )}
            >
              <Icon className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">{item.label}</span>
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border/50">
        <div className="text-xs text-muted-foreground text-center">
          <p>Configurações | set</p>
        </div>
      </div>
    </div>
  );
}
