"use client";

import { CustomerFormProvider as Provider, useCustomerForm } from "@saas/customers/hooks/useCustomerForm";
import { SimpleCustomerFormSheet } from "@saas/customers/components/SimpleCustomerFormSheet";

interface CustomerFormProviderProps {
  children: React.ReactNode;
}

function CustomerFormContent() {
  const { isFormOpen, selectedCustomer, mode, closeForm, handleSave } = useCustomerForm();

  return (
    <SimpleCustomerFormSheet
      isOpen={isFormOpen}
      onClose={closeForm}
      customer={selectedCustomer}
      onSave={handleSave}
      mode={mode}
    />
  );
}

export function CustomerFormProvider({ children }: CustomerFormProviderProps) {
  return (
    <Provider>
      {children}
      <CustomerFormContent />
    </Provider>
  );
}
