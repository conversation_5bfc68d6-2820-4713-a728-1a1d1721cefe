import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { CustomerMetrics } from "@saas/customers/components/CustomerMetrics";
import { CustomersTable } from "@saas/customers/components/CustomersTable";
import { CustomerActionsModal } from "@saas/customers/components/CustomerActionsModal";
import { CustomerFormProvider } from "./CustomerFormProvider";
import { AddCustomerButton } from "@saas/customers/components/AddCustomerButton";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { DownloadIcon, MailIcon, TargetIcon, HeartIcon, MessageSquareIcon, UsersIcon } from "lucide-react";
import { notFound } from "next/navigation";

export default async function CustomersPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  return (
    <CustomerFormProvider>
      <div className="space-y-6">
        <PageHeader
          title="Clientes"
          subtitle="Gerencie seus clientes e acompanhe métricas importantes"
          actions={
            <>
              <Button variant="outline">
                <DownloadIcon className="h-4 w-4 mr-2" />
                Exportar
              </Button>
              <CustomerActionsModal>
                <Button variant="outline">
                  <TargetIcon className="h-4 w-4 mr-2" />
                  Ações Rápidas
                </Button>
              </CustomerActionsModal>
              <AddCustomerButton />
            </>
          }
        />

        {/* Métricas */}
        <CustomerMetrics organizationId={organization.id} />

        {/* Ações Rápidas */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HeartIcon className="h-5 w-5 text-red-500" />
              Ações Rápidas
            </CardTitle>
            <CardDescription>
              Ferramentas essenciais para gerenciar e engajar seus clientes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-4">
              <CustomerActionsModal>
                <Button className="w-full justify-start" variant="outline">
                  <MailIcon className="h-4 w-4 mr-2" />
                  Email Marketing
                </Button>
              </CustomerActionsModal>

              <CustomerActionsModal>
                <Button className="w-full justify-start" variant="outline">
                  <TargetIcon className="h-4 w-4 mr-2" />
                  Segmentar
                </Button>
              </CustomerActionsModal>

              <CustomerActionsModal>
                <Button className="w-full justify-start" variant="outline">
                  <MessageSquareIcon className="h-4 w-4 mr-2" />
                  Follow-up
                </Button>
              </CustomerActionsModal>

              <AddCustomerButton
                variant="outline"
                className="w-full justify-start"
              >
                <UsersIcon className="h-4 w-4 mr-2" />
                Importar
              </AddCustomerButton>
            </div>
          </CardContent>
        </Card>

        {/* Lista de Clientes */}
        <CustomersTable organizationId={organization.id} />
      </div>
    </CustomerFormProvider>
  );
}
