"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@ui/components/sheet";
import { Badge } from "@ui/components/badge";
import { PlusIcon, SparklesIcon, ArrowRightIcon } from "lucide-react";
import { toast } from "sonner";
import { useCreateProduct } from "@saas/products/hooks/useProductsApi";

interface SimpleCreateProductSheetProps {
  organizationId: string;
  organizationSlug?: string;
  trigger?: React.ReactNode;
}

export function SimpleCreateProductSheet({
  organizationId,
  organizationSlug,
  trigger
}: SimpleCreateProductSheetProps) {
  const [open, setOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const router = useRouter();
  const createProduct = useCreateProduct();

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    type: "COURSE" as const,
  });

  // Gerar slug automaticamente baseado no nome
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "") // Remove acentos
      .replace(/[^a-z0-9\s-]/g, "") // Remove caracteres especiais
      .replace(/\s+/g, "-") // Substitui espaços por hífens
      .replace(/-+/g, "-") // Remove hífens duplicados
      .trim();
  };

  const productTypes = [
    { value: "COURSE", label: "Curso", icon: "🎓" },
    { value: "EBOOK", label: "E-book", icon: "📚" },
    { value: "MENTORSHIP", label: "Mentoria", icon: "🤝" },
    { value: "SUBSCRIPTION", label: "Assinatura", icon: "🔄" },
    { value: "BUNDLE", label: "Pacote", icon: "📦" },
  ];

  const selectedType = productTypes.find(type => type.value === formData.type);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Nome do produto é obrigatório");
      return;
    }

    setIsCreating(true);

    try {
      const product = await createProduct.mutateAsync({
        organizationId,
        name: formData.name.trim(),
        slug: generateSlug(formData.name.trim()),
        description: formData.description.trim(),
        type: formData.type,
        priceCents: 0, // Começar gratuito
        currency: "BRL",
        language: "pt-BR",
        status: "DRAFT",
        visibility: "PRIVATE",
        shortDescription: formData.description.trim().substring(0, 100),
        gallery: [],
        tags: [],
        features: [],
        requirements: [],
        certificate: false,
        downloadable: false,
        checkoutType: "DEFAULT",
        settings: {},
      });

      toast.success("Produto criado com sucesso!");
      setOpen(false);

      // Redirecionar para configurações
      const configUrl = organizationSlug
        ? `/app/${organizationSlug}/products/${product.id}/configuracoes`
        : `/app/products/${product.id}/configuracoes`;

      router.push(configUrl);
    } catch (error) {
      console.error("Error creating product:", error);
      toast.error("Erro ao criar produto. Tente novamente.");
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      type: "COURSE",
    });
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      resetForm();
    }
  };

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        {trigger || (
          <Button className="shadow-sm shadow-primary/20 hover:shadow-primary/30 transition-shadow duration-200">
            <PlusIcon className="h-4 w-4 mr-2" />
            Novo Produto
          </Button>
        )}
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-md overflow-y-auto">
        <SheetHeader className="space-y-3">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
              <SparklesIcon className="h-4 w-4 text-primary" />
            </div>
            <SheetTitle className="text-xl">Criar Produto</SheetTitle>
          </div>
          <SheetDescription>
            Preencha apenas o essencial. Você poderá configurar detalhes avançados depois.
          </SheetDescription>
        </SheetHeader>

        <form onSubmit={handleSubmit} className="space-y-6 mt-6">
          {/* Nome do Produto */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium">Nome do Produto *</Label>
            <Input
              id="name"
              placeholder="Ex: Curso de React Avançado"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              maxLength={60}
              required
              className="h-11"
            />
            <p className="text-xs text-muted-foreground">
              {formData.name.length}/60 caracteres
            </p>
          </div>

          {/* Descrição */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">Descrição</Label>
            <Textarea
              id="description"
              placeholder="Descreva brevemente seu produto..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              maxLength={200}
              rows={3}
            />
            <p className="text-xs text-muted-foreground">
              {formData.description.length}/200 caracteres
            </p>
          </div>

          {/* Tipo de Produto */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Tipo de Produto</Label>
            <div className="grid grid-cols-2 gap-2">
              {productTypes.map((type) => (
                <div
                  key={type.value}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    formData.type === type.value
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50"
                  }`}
                  onClick={() => setFormData(prev => ({ ...prev, type: type.value as any }))}
                >
                  <div className="flex flex-col items-center gap-2 text-center">
                    <span className="text-lg">{type.icon}</span>
                    <div className="font-medium text-xs">{type.label}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Preview */}
          {formData.name && (
            <div className="p-4 bg-muted/50 rounded-lg space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                  <span className="text-lg">{selectedType?.icon}</span>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-sm">{formData.name}</h3>
                  <p className="text-xs text-muted-foreground">{selectedType?.label}</p>
                </div>
                <Badge variant="secondary" className="text-xs">
                  Rascunho
                </Badge>
              </div>
              {formData.description && (
                <p className="text-xs text-muted-foreground">{formData.description}</p>
              )}
            </div>
          )}

          {/* Botões */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={!formData.name.trim() || isCreating}
              className="flex-1"
            >
              {isCreating ? (
                "Criando..."
              ) : (
                <>
                  Criar Produto
                  <ArrowRightIcon className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </form>
      </SheetContent>
    </Sheet>
  );
}
