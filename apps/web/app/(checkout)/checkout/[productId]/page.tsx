// app/[locale]/(checkout)/checkout/[productId]/page.tsx
import "server-only";
import { redirect } from 'next/navigation';
import { CheckoutForm } from '../components/checkout-form';
import { CheckoutHeader } from '../components/checkout-header';
import { UrgencyTimer } from "../../../../modules/ui/components/urgency-timer";

interface CheckoutPageProps {
	params: Promise<{
		productId: string;
	}>;
	searchParams: Promise<{
		offer?: string;
		org?: string;
		quick?: string;
	}>;
}

export default async function CheckoutPage({ params, searchParams }: CheckoutPageProps) {
	const { productId } = await params;
	const { offer: offerId, org: organizationSlug, quick } = await searchParams;

	if (!productId) {
		redirect('/');
	}

	// Fetch product data from API
	try {
		const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
		const url = new URL(`${baseUrl}/api/checkout/product/${productId}`);
		if (offerId) {
			url.searchParams.set('offer', offerId);
		}

		const response = await fetch(url.toString());

		if (!response.ok) {
			console.error('Failed to fetch product:', response.statusText);
			redirect('/');
		}

		const product = await response.json();

		return (
			<div className='min-h-screen'>
				<CheckoutHeader />

				{/* Show urgency timer for offers */}
				{offerId && (
					<div className='mx-3 md:mx-auto md:container py-5'>
						<UrgencyTimer
							endTime={new Date(Date.now() + 2 * 60 * 60 * 1000)} // 2 horas
							message="Oferta especial expira em:"
							variant="warning"
						/>
					</div>
				)}

				<div className='mx-3 md:mx-auto md:container py-5'>
					{/* Banner */}
					{product.thumbnail && (
						<div className='w-full mb-6'>
							<img
								src={product.thumbnail}
								alt='Checkout banner'
								className='w-full rounded-lg shadow-sm'
							/>
						</div>
					)}

					<CheckoutForm product={product} offerId={offerId} />
				</div>
			</div>
		);
	} catch (error) {
		console.error('Error fetching product:', error);
		redirect('/');
	}

	try {
		const product = await db.product.findFirst({
			where: {
				id: productId,
				status: 'PUBLISHED',
			},
		});

		if (!product) {
			redirect('/');
		}

		// Buscar creator e offers separadamente
		const [creator, offers] = await Promise.all([
			db.user.findUnique({
				where: { id: product.creatorId },
				select: { id: true, name: true },
			}),
			db.offer.findMany({
				where: {
					productId: product.id,
					isActive: true,
					type: 'ORDER_BUMP',
				},
				select: {
					id: true,
					name: true,
					valueCents: true,
					type: true,
				},
			}),
		]);

		console.log('product', {
			id: product.id,
			settings: product.settings,
		});

		// Handle external checkout redirection
		if (product.checkoutType === 'EXTERNAL' && product.settings) {
			const settings = product.settings as Record<string, unknown>;
			if (typeof settings.customCheckoutUrl === 'string') {
				redirect(settings.customCheckoutUrl);
			}
		}

		return (
			<div className='min-h-screen'>
				<CheckoutHeader />

				<div className='mx-3 md:mx-auto md:container py-5  '>
					{/* Display a banner if available from settings */}
					{(() => {
						let bannerUrl: string | null = null;

						// Check in settings
						if (product.settings && typeof product.settings === 'object') {
							const settings = product.settings as Record<string, unknown>;
							if (typeof settings.banner === 'string') {
								bannerUrl = settings.banner;
							}
						}

						// Return banner element if URL is found
						return bannerUrl ? (
							<div className='w-full mb-6'>
								<img
									src={bannerUrl}
									alt='Checkout banner'
									className='w-full rounded-lg shadow-sm'
								/>
							</div>
						) : null;
					})()}
					<CheckoutForm
						product={{
							id: product.id,
							title: product.name,
							description: product.description,
							type: product.type === 'MENTORSHIP' ? 'MENTORING' : product.type as 'COURSE' | 'EBOOK' | 'MENTORING',
							price: Number(product.priceCents) / 100,
							installmentsLimit: 12, // Default value
							enableInstallments: true, // Default value
							thumbnail: product.thumbnail,
							checkoutType: product.checkoutType,
							acceptedPayments: ['CREDIT_CARD', 'PIX'], // Default values
							checkoutSettings: product.settings,
							customCheckoutUrl: null,
							successUrl: null,
							cancelUrl: null,
							termsUrl: null,
							offers: offers?.map((o) => ({
								id: o.id,
								title: o.name,
								description: null,
								price: Number(o.valueCents) / 100,
								type: o.type,
							})),
						}}
					/>
				</div>
			</div>
		);
	} catch (error) {
		console.error('Error loading product for checkout:', error);
		redirect('/');
	}
}
