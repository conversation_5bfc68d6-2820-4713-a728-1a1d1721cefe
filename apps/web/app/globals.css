@import "tailwindcss";
@import "fumadocs-ui/css/neutral.css";
@import "fumadocs-ui/css/preset.css";
@import "@repo/tailwind-config/theme.css";
@import "@repo/tailwind-config/tailwind-animate.css";

@source "../node_modules/fumadocs-ui/dist/**/*.js";

@variant dark (&:where(.dark, .dark *));

@layer base {
	:root {
		/* Chart colors */
		--chart-1: oklch(0.646 0.222 41.116);
		--chart-2: oklch(0.6 0.118 184.704);
		--chart-3: oklch(0.398 0.07 227.392);
		--chart-4: oklch(0.828 0.189 84.429);
		--chart-5: oklch(0.769 0.188 70.08);
	}

	.dark {
		/* Chart colors - dark mode */
		--chart-1: oklch(0.488 0.243 264.376);
		--chart-2: oklch(0.696 0.17 162.48);
		--chart-3: oklch(0.769 0.188 70.08);
		--chart-4: oklch(0.627 0.265 303.9);
		--chart-5: oklch(0.645 0.246 16.439);
	}
}

pre.shiki {
	@apply mb-4 rounded-lg p-6;
}

#nd-sidebar {
	@apply bg-card! top-[4.5rem] md:h-[calc(100dvh-4.5rem)]!;

	button[data-search-full] {
		@apply bg-transparent;
	}
}

#nd-page .prose {
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		a {
			@apply no-underline!;
		}
	}
}

div[role="tablist"].bg-secondary {
	@apply bg-muted!;
}

input[cmdk-input] {
	@apply border-none focus-visible:ring-0;
}
