"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import { useTranslations } from "next-intl";
import { KeyIcon, MailIcon } from "lucide-react";

export function LoginModeSwitch({
	activeMode,
	onChange,
	className,
}: {
	activeMode: "password" | "magic-link";
	onChange: (mode: string) => void;
	className?: string;
}) {
	const t = useTranslations();
	return (
		<Tabs value={activeMode} onValueChange={onChange} className={className}>
			<TabsList className="w-full h-12 bg-muted/30 p-1 rounded-xl">
				<TabsTrigger
					value="password"
					className="flex-1 data-[state=active]:bg-background data-[state=active]:shadow-lg transition-all duration-300 data-[state=active]:scale-[1.02] rounded-lg flex items-center gap-2"
				>
					<KeyIcon className="h-4 w-4" />
					{t("auth.login.modes.password")}
				</TabsTrigger>
				<TabsTrigger
					value="magic-link"
					className="flex-1 data-[state=active]:bg-background data-[state=active]:shadow-lg transition-all duration-300 data-[state=active]:scale-[1.02] rounded-lg flex items-center gap-2"
				>
					<MailIcon className="h-4 w-4" />
					{t("auth.login.modes.magicLink")}
				</TabsTrigger>
			</TabsList>
		</Tabs>
	);
}
