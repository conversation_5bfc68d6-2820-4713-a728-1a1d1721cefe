"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useAuthErrorMessages } from "@saas/auth/hooks/errors-messages";
import { OrganizationInvitationAlert } from "@saas/organizations/components/OrganizationInvitationAlert";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	AlertTriangleIcon,
	ArrowRightIcon,
	CheckCircleIcon,
	EyeIcon,
	EyeOffIcon,
	LoaderIcon,
	MailboxIcon,
	UserIcon,
} from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { withQuery } from "ufo";
import { z } from "zod";
import {
	type OAuthProvider,
	oAuthProviders,
} from "../constants/oauth-providers";
import { SocialSigninButton } from "./SocialSigninButton";

const formSchema = z.object({
	email: z.string().email(),
	name: z.string().min(1),
	password: z.string(),
});

export function SignupForm({ prefillEmail }: { prefillEmail?: string }) {
	const t = useTranslations();
	const router = useRouter();
	const { getAuthErrorMessage } = useAuthErrorMessages();
	const searchParams = useSearchParams();

	const [showPassword, setShowPassword] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [submitSuccess, setSubmitSuccess] = useState(false);
	const invitationId = searchParams.get("invitationId");
	const email = searchParams.get("email");
	const redirectTo = searchParams.get("redirectTo");

	const form = useForm({
		resolver: zodResolver(formSchema),
		values: {
			name: "",
			email: prefillEmail ?? email ?? "",
			password: "",
		},
	});

	const invitationOnlyMode = !config.auth.enableSignup && invitationId;

	const redirectPath = invitationId
		? `/organization-invitation/${invitationId}`
		: (redirectTo ?? config.auth.redirectAfterSignIn);

	const onSubmit = form.handleSubmit(async ({ email, password, name }) => {
		setIsSubmitting(true);
		setSubmitSuccess(false);

		try {
			const { error } = await (config.auth.enablePasswordLogin
				? await authClient.signUp.email({
						email,
						password,
						name,
						callbackURL: redirectPath,
					})
				: authClient.signIn.magicLink({
						email,
						name,
						callbackURL: redirectPath,
					}));

			if (error) {
				throw error;
			}

			if (invitationOnlyMode) {
				const { error } =
					await authClient.organization.acceptInvitation({
						invitationId,
					});

				if (error) {
					throw error;
				}

				router.push(config.auth.redirectAfterSignIn);
			} else {
				setSubmitSuccess(true);
			}
		} catch (e) {
			form.setError("root", {
				message: getAuthErrorMessage(
					e && typeof e === "object" && "code" in e
						? (e.code as string)
						: undefined,
				),
			});
		} finally {
			setIsSubmitting(false);
		}
	});

	return (
		<div className="flex flex-col gap-y-6">
			{/* Header com título e subtítulo */}
			<div className="text-center space-y-2 animate-in fade-in-50 duration-500">
				<h1 className="text-2xl font-bold text-foreground">
					{t("auth.signup.title")}
				</h1>
				<p className="text-muted-foreground text-sm">
					{t("auth.signup.subtitle")}
				</p>
			</div>

			{(form.formState.isSubmitSuccessful && !invitationOnlyMode) || submitSuccess ? (
				<Alert variant="success" className="animate-in slide-in-from-top-2 duration-300">
					<CheckCircleIcon className="h-4 w-4" />
					<AlertTitle>
						{t("auth.signup.hints.verifyEmail")}
					</AlertTitle>
					<AlertDescription>
						{t("auth.signup.hints.verifyEmail")}
					</AlertDescription>
				</Alert>
			) : (
				<>
					{invitationId && (
						<OrganizationInvitationAlert className="mb-6" />
					)}

					<Form {...form}>
						<form
							className="space-y-6"
							onSubmit={onSubmit}
						>
							{form.formState.isSubmitted &&
								form.formState.errors.root && (
									<Alert variant="error" className="animate-in slide-in-from-top-2 duration-300">
										<AlertTriangleIcon className="h-4 w-4" />
										<AlertTitle>
											{form.formState.errors.root.message}
										</AlertTitle>
									</Alert>
								)}

							<FormField
								control={form.control}
								name="name"
								render={({ field, fieldState }) => (
									<FormItem className="animate-in slide-in-from-left-2 duration-300">
										<FormLabel className="text-sm font-medium">
											{t("auth.signup.name")}
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												className={`h-11 transition-all duration-200 ${
													fieldState.error
														? "border-destructive focus-visible:ring-destructive"
														: fieldState.isDirty && !fieldState.error
														? "border-green-500 focus-visible:ring-green-500"
														: ""
												}`}
												placeholder="Seu nome completo"
											/>
										</FormControl>
										{fieldState.error && (
											<p className="text-sm text-destructive animate-in fade-in-50 duration-200">
												{fieldState.error.message}
											</p>
										)}
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="email"
								render={({ field, fieldState }) => (
									<FormItem className="animate-in slide-in-from-right-2 duration-300">
										<FormLabel className="text-sm font-medium">
											{t("auth.signup.email")}
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												autoComplete="email"
												readOnly={!!prefillEmail}
												className={`h-11 transition-all duration-200 ${
													fieldState.error
														? "border-destructive focus-visible:ring-destructive"
														: fieldState.isDirty && !fieldState.error
														? "border-green-500 focus-visible:ring-green-500"
														: ""
												}`}
												placeholder="<EMAIL>"
											/>
										</FormControl>
										{fieldState.error && (
											<p className="text-sm text-destructive animate-in fade-in-50 duration-200">
												{fieldState.error.message}
											</p>
										)}
									</FormItem>
								)}
							/>

							{config.auth.enablePasswordLogin && (
								<FormField
									control={form.control}
									name="password"
									render={({ field, fieldState }) => (
										<FormItem className="animate-in slide-in-from-left-2 duration-300">
											<FormLabel className="text-sm font-medium">
												{t("auth.signup.password")}
											</FormLabel>
											<FormControl>
												<div className="relative">
													<Input
														type={
															showPassword
																? "text"
																: "password"
														}
														className={`pr-10 h-11 transition-all duration-200 ${
															fieldState.error
																? "border-destructive focus-visible:ring-destructive"
																: fieldState.isDirty && !fieldState.error
																? "border-green-500 focus-visible:ring-green-500"
																: ""
														}`}
														{...field}
														autoComplete="new-password"
														placeholder="••••••••"
													/>
													<button
														type="button"
														onClick={() =>
															setShowPassword(
																!showPassword,
															)
														}
														className="absolute inset-y-0 right-0 flex items-center pr-4 text-muted-foreground hover:text-foreground transition-all duration-200 hover:scale-110"
													>
														{showPassword ? (
															<EyeOffIcon className="size-4" />
														) : (
															<EyeIcon className="size-4" />
														)}
													</button>
												</div>
											</FormControl>
											{fieldState.error && (
												<p className="text-sm text-destructive animate-in fade-in-50 duration-200">
													{fieldState.error.message}
												</p>
											)}
										</FormItem>
									)}
								/>
							)}

							<Button
								className="w-full h-11 transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
								loading={isSubmitting}
								disabled={isSubmitting}
							>
								{isSubmitting ? (
									<>
										<LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
										{t("auth.signup.loading.creating")}
									</>
								) : (
									<>
										<UserIcon className="mr-2 h-4 w-4" />
										{t("auth.signup.submit")}
									</>
								)}
							</Button>
						</form>
					</Form>

					{config.auth.enableSignup &&
						config.auth.enableSocialLogin && (
							<>
								<div className="space-y-4 animate-in fade-in-50 duration-500">
									<div className="text-center">
										<h3 className="text-sm font-medium text-foreground mb-1">
											{t("auth.signup.hints.socialSignup.title")}
										</h3>
										<p className="text-xs text-muted-foreground">
											{t("auth.signup.hints.socialSignup.description")}
										</p>
									</div>

									<div className="relative my-6 h-4">
										<hr className="relative top-2 border-muted-foreground/20" />
										<p className="-translate-x-1/2 absolute top-0 left-1/2 mx-auto inline-block h-4 bg-card px-3 text-center font-medium text-muted-foreground text-sm leading-tight">
											{t("auth.login.continueWith")}
										</p>
									</div>
								</div>

								<div className="grid grid-cols-1 items-stretch gap-3 animate-in slide-in-from-bottom-2 duration-300">
									{Object.keys(oAuthProviders).map(
										(providerId, index) => (
											<div
												key={providerId}
												className="animate-in slide-in-from-bottom-2 duration-300"
												style={{
													animationDelay: `${index * 100}ms`,
												}}
											>
												<SocialSigninButton
													provider={
														providerId as OAuthProvider
													}
												/>
											</div>
										),
									)}
								</div>
							</>
						)}

					{/* Seção de benefícios */}
					<div className="mt-8 p-4 bg-muted/30 rounded-lg animate-in fade-in-50 duration-500">
						<h4 className="text-sm font-medium text-foreground mb-3 text-center">
							{t("auth.signup.hints.benefits.title")}
						</h4>
						<div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
							{[
								t("auth.signup.hints.benefits.items.0"),
								t("auth.signup.hints.benefits.items.1"),
								t("auth.signup.hints.benefits.items.2"),
								t("auth.signup.hints.benefits.items.3"),
							].map((item: string, index: number) => (
								<div key={index} className="flex items-center gap-2">
									<CheckCircleIcon className="h-3 w-3 text-green-500 flex-shrink-0" />
									<span>{item}</span>
								</div>
							))}
						</div>
					</div>

					<div className="mt-8 text-center text-sm animate-in fade-in-50 duration-500">
						<span className="text-muted-foreground">
							{t("auth.signup.alreadyHaveAccount")}{" "}
						</span>
						<Link
							href={withQuery(
								"/auth/login",
								Object.fromEntries(searchParams.entries()),
							)}
							className="text-foreground hover:text-primary transition-all duration-200 font-medium hover:underline inline-flex items-center group"
						>
							{t("auth.signup.signIn")}
							<ArrowRightIcon className="ml-1 inline size-4 align-middle transition-transform duration-200 group-hover:translate-x-1" />
						</Link>
					</div>

					{/* Seção de ajuda */}
					<div className="mt-8 pt-6 border-t border-border/50 animate-in fade-in-50 duration-700">
						<div className="text-center space-y-3">
							<h4 className="text-sm font-medium text-foreground">
								Precisa de ajuda?
							</h4>
							<div className="flex flex-col sm:flex-row gap-3 justify-center text-xs text-muted-foreground">
								<Link
									href="/contact"
									className="hover:text-foreground transition-colors hover:underline"
								>
									Suporte técnico
								</Link>
								<span className="hidden sm:inline">•</span>
								<Link
									href="/docs"
									className="hover:text-foreground transition-colors hover:underline"
								>
									Documentação
								</Link>
								<span className="hidden sm:inline">•</span>
								<Link
									href="/pricing"
									className="hover:text-foreground transition-colors hover:underline"
								>
									Preços
								</Link>
							</div>
						</div>
					</div>
				</>
			)}
		</div>
	);
}
