"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useAuthErrorMessages } from "@saas/auth/hooks/errors-messages";
import { sessionQuery<PERSON>ey } from "@saas/auth/lib/api";
import { OrganizationInvitationAlert } from "@saas/organizations/components/OrganizationInvitationAlert";
import { useRouter } from "@shared/hooks/router";
import { useQueryClient } from "@tanstack/react-query";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	AlertTriangleIcon,
	ArrowRightIcon,
	CheckCircleIcon,
	EyeIcon,
	EyeOffIcon,
	KeyIcon,
	LoaderIcon,
	MailboxIcon,
} from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { withQuery } from "ufo";
import { z } from "zod";
import {
	type OAuthProvider,
	oAuthProviders,
} from "../constants/oauth-providers";
import { useSession } from "../hooks/use-session";
import { LoginModeSwitch } from "./LoginModeSwitch";
import { SocialSigninButton } from "./SocialSigninButton";

const formSchema = z.union([
	z.object({
		mode: z.literal("magic-link"),
		email: z.string().email(),
	}),
	z.object({
		mode: z.literal("password"),
		email: z.string().email(),
		password: z.string().min(1),
	}),
]);

type FormValues = z.infer<typeof formSchema>;

export function LoginForm() {
	const t = useTranslations();
	const { getAuthErrorMessage } = useAuthErrorMessages();
	const router = useRouter();
	const queryClient = useQueryClient();
	const searchParams = useSearchParams();
	const { user, loaded: sessionLoaded } = useSession();

	const [showPassword, setShowPassword] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [submitSuccess, setSubmitSuccess] = useState(false);
	const [isPasskeyLoading, setIsPasskeyLoading] = useState(false);
	const invitationId = searchParams.get("invitationId");
	const email = searchParams.get("email");
	const redirectTo = searchParams.get("redirectTo");

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: email ?? "",
			password: "",
			mode: config.auth.enablePasswordLogin ? "password" : "magic-link",
		},
	});

	const redirectPath = invitationId
		? `/organization-invitation/${invitationId}`
		: (redirectTo ?? config.auth.redirectAfterSignIn);

	useEffect(() => {
		if (sessionLoaded && user) {
			router.replace(redirectPath);
		}
	}, [user, sessionLoaded]);

	const onSubmit: SubmitHandler<FormValues> = async (values) => {
		setIsSubmitting(true);
		setSubmitSuccess(false);

		try {
			if (values.mode === "password") {
				const { data, error } = await authClient.signIn.email({
					...values,
				});

				if (error) {
					throw error;
				}

				if ((data as any).twoFactorRedirect) {
					router.replace(
						withQuery(
							"/auth/verify",
							Object.fromEntries(searchParams.entries()),
						),
					);
					return;
				}

				queryClient.invalidateQueries({
					queryKey: sessionQueryKey,
				});

				router.replace(redirectPath);
			} else {
				const { error } = await authClient.signIn.magicLink({
					...values,
					callbackURL: redirectPath,
				});

				if (error) {
					throw error;
				}

				setSubmitSuccess(true);
			}
		} catch (e) {
			form.setError("root", {
				message: getAuthErrorMessage(
					e && typeof e === "object" && "code" in e
						? (e.code as string)
						: undefined,
				),
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const signInWithPasskey = async () => {
		setIsPasskeyLoading(true);
		try {
			await authClient.signIn.passkey();
			router.replace(redirectPath);
		} catch (e) {
			form.setError("root", {
				message: getAuthErrorMessage(
					e && typeof e === "object" && "code" in e
						? (e.code as string)
						: undefined,
				),
			});
		} finally {
			setIsPasskeyLoading(false);
		}
	};

	const signinMode = form.watch("mode");

	return (
		<div className="flex flex-col gap-y-6">
			{/* Header com título e subtítulo */}
			<div className="text-center space-y-2 animate-in fade-in-50 duration-500">
				<h1 className="text-2xl font-bold text-foreground">
					{t("auth.login.title")}
				</h1>
				<p className="text-muted-foreground text-sm">
					{t("auth.login.subtitle")}
				</p>
			</div>

			{(form.formState.isSubmitSuccessful && signinMode === "magic-link") || submitSuccess ? (
				<Alert variant="success" className="animate-in slide-in-from-top-2 duration-300">
					<CheckCircleIcon className="h-4 w-4" />
					<AlertTitle>
						{t("auth.login.hints.linkSent.title")}
					</AlertTitle>
					<AlertDescription>
						{t("auth.login.hints.linkSent.message")}
					</AlertDescription>
				</Alert>
			) : (
				<>
					{invitationId && (
						<OrganizationInvitationAlert className="mb-6" />
					)}

					<Form {...form}>
						<form
							className="space-y-6"
							onSubmit={form.handleSubmit(onSubmit)}
						>
							{config.auth.enableMagicLink &&
								config.auth.enablePasswordLogin && (
									<div className="space-y-4 animate-in fade-in-50 duration-300">
										<LoginModeSwitch
											activeMode={signinMode}
											onChange={(mode) =>
												form.setValue(
													"mode",
													mode as typeof signinMode,
												)
											}
										/>
										<div className="text-center">
											<p className="text-xs text-muted-foreground">
												{signinMode === "password"
													? t("auth.login.hints.passwordMode.description")
													: t("auth.login.hints.magicLinkMode.description")
												}
											</p>
										</div>
									</div>
								)}

							{form.formState.isSubmitted &&
								form.formState.errors.root?.message && (
									<Alert variant="error" className="animate-in slide-in-from-top-2 duration-300">
										<AlertTriangleIcon className="h-4 w-4" />
										<AlertTitle>
											{form.formState.errors.root.message}
										</AlertTitle>
									</Alert>
								)}

							<FormField
								control={form.control}
								name="email"
								render={({ field, fieldState }) => (
									<FormItem className="animate-in slide-in-from-left-2 duration-300">
										<FormLabel className="text-sm font-medium">
											{t("auth.signup.email")}
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												autoComplete="email"
												placeholder="<EMAIL>"
												className={`h-11 transition-all duration-200 ${
													fieldState.error
														? "border-destructive focus-visible:ring-destructive"
														: fieldState.isDirty && !fieldState.error
														? "border-green-500 focus-visible:ring-green-500"
														: ""
												}`}
											/>
										</FormControl>
										{fieldState.error && (
											<p className="text-sm text-destructive animate-in fade-in-50 duration-200">
												{fieldState.error.message}
											</p>
										)}
									</FormItem>
								)}
							/>

							{config.auth.enablePasswordLogin &&
								signinMode === "password" && (
									<FormField
										control={form.control}
										name="password"
										render={({ field, fieldState }) => (
											<FormItem className="animate-in slide-in-from-right-2 duration-300">
												<div className="flex justify-between gap-4">
													<FormLabel className="text-sm font-medium">
														{t(
															"auth.signup.password",
														)}
													</FormLabel>

													<Link
														href="/auth/forgot-password"
														className="text-muted-foreground text-xs hover:text-foreground transition-colors hover:underline"
													>
														{t(
															"auth.login.forgotPassword",
														)}
													</Link>
												</div>
												<FormControl>
													<div className="relative">
														<Input
															type={
																showPassword
																	? "text"
																	: "password"
															}
															className={`pr-10 h-11 transition-all duration-200 ${
																fieldState.error
																	? "border-destructive focus-visible:ring-destructive"
																	: fieldState.isDirty && !fieldState.error
																	? "border-green-500 focus-visible:ring-green-500"
																	: ""
															}`}
															{...field}
															autoComplete="current-password"
															placeholder="••••••••"
														/>
														<button
															type="button"
															onClick={() =>
																setShowPassword(
																	!showPassword,
																)
															}
															className="absolute inset-y-0 right-0 flex items-center pr-4 text-muted-foreground hover:text-foreground transition-all duration-200 hover:scale-110"
														>
															{showPassword ? (
																<EyeOffIcon className="size-4" />
															) : (
																<EyeIcon className="size-4" />
															)}
														</button>
													</div>
												</FormControl>
												{fieldState.error && (
													<p className="text-sm text-destructive animate-in fade-in-50 duration-200">
														{fieldState.error.message}
													</p>
												)}
											</FormItem>
										)}
									/>
								)}

							<Button
								className="w-full h-11 transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
								type="submit"
								loading={isSubmitting}
								disabled={isSubmitting}
							>
								{isSubmitting ? (
									<>
										<LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
										{signinMode === "magic-link"
											? t("auth.login.loading.sendingLink")
											: t("auth.login.loading.signingIn")}
									</>
								) : (
									<>
										{signinMode === "magic-link"
											? t("auth.login.sendMagicLink")
											: t("auth.login.submit")}
									</>
								)}
							</Button>
						</form>
					</Form>

					{(config.auth.enablePasskeys ||
						(config.auth.enableSignup &&
							config.auth.enableSocialLogin)) && (
						<>
							<div className="space-y-4 animate-in fade-in-50 duration-500">
								<div className="text-center">
									<h3 className="text-sm font-medium text-foreground mb-1">
										{t("auth.login.hints.socialLogin.title")}
									</h3>
									<p className="text-xs text-muted-foreground">
										{t("auth.login.hints.socialLogin.description")}
									</p>
								</div>

								<div className="relative my-6 h-4">
									<hr className="relative top-2 border-muted-foreground/20" />
									<p className="-translate-x-1/2 absolute top-0 left-1/2 mx-auto inline-block h-4 bg-card px-3 text-center font-medium text-muted-foreground text-sm leading-tight">
										{t("auth.login.continueWith")}
									</p>
								</div>
							</div>

							<div className="grid grid-cols-1 items-stretch gap-3 animate-in slide-in-from-bottom-2 duration-300">
								{config.auth.enableSignup &&
									config.auth.enableSocialLogin &&
									Object.keys(oAuthProviders).map(
										(providerId, index) => (
											<div
												key={providerId}
												className="animate-in slide-in-from-bottom-2 duration-300"
												style={{
													animationDelay: `${index * 100}ms`,
												}}
											>
												<SocialSigninButton
													provider={
														providerId as OAuthProvider
													}
												/>
											</div>
										),
									)}

								{config.auth.enablePasskeys && (
									<div className="space-y-2">
										<Button
											variant="outline"
											className="w-full h-11 transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] hover:bg-muted/50"
											onClick={() => signInWithPasskey()}
											disabled={isPasskeyLoading}
										>
											{isPasskeyLoading ? (
												<>
													<LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
													{t("auth.login.loading.authenticating")}
												</>
											) : (
												<>
													<KeyIcon className="mr-2 size-4" />
													{t("auth.login.loginWithPasskey")}
												</>
											)}
										</Button>
										<p className="text-xs text-muted-foreground text-center">
											{t("auth.login.hints.passkey.description")}
										</p>
									</div>
								)}
							</div>
						</>
					)}

					{config.auth.enableSignup && (
						<div className="mt-8 text-center text-sm animate-in fade-in-50 duration-500">
							<span className="text-muted-foreground">
								{t("auth.login.dontHaveAnAccount")}{" "}
							</span>
							<Link
								href={withQuery(
									"/auth/signup",
									Object.fromEntries(searchParams.entries()),
								)}
								className="text-foreground hover:text-primary transition-all duration-200 font-medium hover:underline inline-flex items-center group"
							>
								{t("auth.login.createAnAccount")}
								<ArrowRightIcon className="ml-1 inline size-4 align-middle transition-transform duration-200 group-hover:translate-x-1" />
							</Link>
						</div>
					)}

					{/* Seção de ajuda */}
					<div className="mt-8 pt-6 border-t border-border/50 animate-in fade-in-50 duration-700">
						<div className="text-center space-y-3">
							<h4 className="text-sm font-medium text-foreground">
								Precisa de ajuda?
							</h4>
							<div className="flex flex-col sm:flex-row gap-3 justify-center text-xs text-muted-foreground">
								<Link
									href="/auth/forgot-password"
									className="hover:text-foreground transition-colors hover:underline"
								>
									Esqueceu sua senha?
								</Link>
								<span className="hidden sm:inline">•</span>
								<Link
									href="/contact"
									className="hover:text-foreground transition-colors hover:underline"
								>
									Suporte técnico
								</Link>
								<span className="hidden sm:inline">•</span>
								<Link
									href="/docs"
									className="hover:text-foreground transition-colors hover:underline"
								>
									Documentação
								</Link>
							</div>
						</div>
					</div>
				</>
			)}
		</div>
	);
}
