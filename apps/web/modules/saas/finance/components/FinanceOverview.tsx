"use client";

import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { WithdrawModal } from "./WithdrawModal";
import {
  TrendingUpIcon,
  TrendingDownIcon,
  DollarSignIcon,
  CreditCardIcon,
  ClockIcon,
  BarChart3Icon,
  CalendarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PiggyBankIcon,
  WalletIcon,
  RefreshCcw,
  ShieldCheckIcon,
  DownloadIcon,
  ArrowUpRightIcon,
} from "lucide-react";

interface FinanceOverviewProps {
  organizationId: string;
}

// Mock data - replace with real API calls
const mockMetrics = {
  totalBalance: 125430.50,
  availableBalance: 98750.25,
  pendingBalance: 26680.25,
  totalRevenue: 542890.75,
  monthlyRevenue: 45230.80,
  totalFees: 12450.30,
  totalWithdraws: 89650.40,
  transactionCount: 2847,
  pendingTransactions: 23,
  failedTransactions: 5,
};

const mockRecentTransactions = [
  {
    id: "1",
    type: "CREDIT",
    description: "Venda - Curso Premium",
    amount: 1500.00,
    status: "COMPLETED",
    createdAt: "2024-01-20T10:30:00Z",
    paymentMethod: "PIX",
  },
  {
    id: "2",
    type: "DEBIT",
    description: "Taxa de Processamento",
    amount: -45.00,
    status: "COMPLETED",
    createdAt: "2024-01-20T09:15:00Z",
    paymentMethod: "PLATFORM_FEE",
  },
  {
    id: "3",
    type: "CREDIT",
    description: "Venda - Consultoria",
    amount: 2500.00,
    status: "PENDING",
    createdAt: "2024-01-20T08:45:00Z",
    paymentMethod: "CREDIT_CARD",
  },
];

export function FinanceOverview({ organizationId }: FinanceOverviewProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      COMPLETED: { label: "Concluída", variant: "default" as const },
      PENDING: { label: "Pendente", variant: "secondary" as const },
      FAILED: { label: "Falhou", variant: "destructive" as const },
      PROCESSING: { label: "Processando", variant: "outline" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTransactionIcon = (type: string) => {
    return type === "CREDIT" ? (
      <ArrowUpIcon className="h-4 w-4 text-green-600" />
    ) : (
      <ArrowDownIcon className="h-4 w-4 text-red-600" />
    );
  };

  return (
    <div className="space-y-6">
      {/* Financial Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white relative overflow-hidden">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm font-medium">Saldo Disponível</p>
                <p className="text-3xl font-bold">
                  {formatCurrency(mockMetrics.availableBalance)}
                </p>
                <p className="text-green-100 text-xs">
                  Pronto para saque
                </p>
                <p className="text-green-200 text-xs mt-1">
                  Total: {formatCurrency(mockMetrics.totalBalance)}
                </p>
              </div>
              <WalletIcon className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Receita Mensal</CardTitle>
            <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(mockMetrics.monthlyRevenue)}
            </div>
            <div className="flex items-center gap-1 text-xs text-green-600">
              <TrendingUpIcon className="h-3 w-3" />
              <span>+12.5%</span>
              <span className="text-muted-foreground">vs mês anterior</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Em Processamento</CardTitle>
            <ClockIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {formatCurrency(mockMetrics.pendingBalance)}
            </div>
            <p className="text-xs text-muted-foreground">
              {mockMetrics.pendingTransactions} transações aguardando liquidação
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Saques Realizados</CardTitle>
            <PiggyBankIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(mockMetrics.totalWithdraws)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total sacado este mês
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Transactions and Quick Actions */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Transactions */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Transações Recentes</CardTitle>
                <CardDescription>
                  Últimas movimentações financeiras
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                Ver Todas
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockRecentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getTransactionIcon(transaction.type)}
                    <div>
                      <p className="text-sm font-medium">{transaction.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDate(transaction.createdAt)} • {transaction.paymentMethod}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-sm font-medium ${
                      transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatCurrency(Math.abs(transaction.amount))}
                    </p>
                    {getStatusBadge(transaction.status)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Banking Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Operações Bancárias</CardTitle>
            <CardDescription>
              Gerencie seus fundos e identidade
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              <WithdrawModal availableBalance={mockMetrics.availableBalance}>
                <Button className="w-full justify-start" variant="default">
                  <ArrowUpRightIcon className="h-4 w-4 mr-2" />
                  Solicitar Saque
                </Button>
              </WithdrawModal>
              <Button className="w-full justify-start" variant="outline">
                <ShieldCheckIcon className="h-4 w-4 mr-2" />
                Validar Identidade
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <RefreshCcw className="h-4 w-4 mr-2" />
                Sincronizar Conta
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <DownloadIcon className="h-4 w-4 mr-2" />
                Extrato Detalhado
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

    </div>
  );
}
