"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@ui/components/chart";
import { Area, AreaChart, Bar, BarChart, CartesianGrid, Pie, PieChart, XAxis, YAxis } from "recharts";
import {
  TrendingUpIcon,
  BarChart3Icon,
  PieChartIcon,
  CalendarIcon
} from "lucide-react";

interface FinancialChartsProps {
  organizationId: string;
}

// Dados de exemplo para demonstração
const monthlyRevenueData = [
  { month: "Jan", revenue: 45000, transactions: 120 },
  { month: "Fev", revenue: 52000, transactions: 145 },
  { month: "Mar", revenue: 48000, transactions: 132 },
  { month: "Abr", revenue: 61000, transactions: 168 },
  { month: "Mai", revenue: 55000, transactions: 152 },
  { month: "Jun", revenue: 67000, transactions: 185 },
  { month: "Jul", revenue: 59000, transactions: 163 },
  { month: "Ago", revenue: 72000, transactions: 198 },
  { month: "Set", revenue: 65000, transactions: 179 },
  { month: "Out", revenue: 58000, transactions: 160 },
  { month: "Nov", revenue: 63000, transactions: 174 },
  { month: "Dez", revenue: 71000, transactions: 196 },
];

const categoryData = [
  { name: "Cursos", value: 45000, color: "hsl(var(--chart-1))" },
  { name: "E-books", value: 28000, color: "hsl(var(--chart-2))" },
  { name: "Mentorias", value: 15000, color: "hsl(var(--chart-3))" },
  { name: "Assinaturas", value: 12000, color: "hsl(var(--chart-4))" },
  { name: "Outros", value: 8000, color: "hsl(var(--chart-5))" },
];

export function FinancialCharts({ organizationId }: FinancialChartsProps) {
  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Gráfico de Receita */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <TrendingUpIcon className="h-5 w-5" />
                Receita Mensal
              </CardTitle>
              <CardDescription>
                Evolução da receita nos últimos 12 meses
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <CalendarIcon className="h-4 w-4 mr-2" />
              Período
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              revenue: {
                label: "Receita",
                color: "hsl(var(--chart-1))",
              },
            }}
            className="min-h-64 w-full"
          >
            <AreaChart data={monthlyRevenueData} accessibilityLayer>
              <defs>
                <linearGradient id="fillRevenue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#4e6df5" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#4e6df5" stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="month"
                tickLine={false}
                axisLine={false}
                tickMargin={10}
                className="text-xs fill-muted-foreground"
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                tickMargin={10}
                tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
                className="text-xs fill-muted-foreground"
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelFormatter={(value) => `Mês: ${value}`}
                    formatter={(value) => [
                      `R$ ${Number(value).toLocaleString()}`,
                      "Receita"
                    ]}
                  />
                }
              />
              <Area
                type="monotone"
                dataKey="revenue"
                stroke="#4e6df5"
                fill="url(#fillRevenue)"
                strokeWidth={2}
              />
            </AreaChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Gráfico de Categorias */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <PieChartIcon className="h-5 w-5" />
                Receita por Categoria
              </CardTitle>
              <CardDescription>
                Distribuição da receita por tipo de produto
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <CalendarIcon className="h-4 w-4 mr-2" />
              Período
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              value: {
                label: "Valor",
              },
              Cursos: {
                label: "Cursos",
                color: "hsl(var(--chart-1))",
              },
              "E-books": {
                label: "E-books",
                color: "hsl(var(--chart-2))",
              },
              Mentorias: {
                label: "Mentorias",
                color: "hsl(var(--chart-3))",
              },
              Assinaturas: {
                label: "Assinaturas",
                color: "hsl(var(--chart-4))",
              },
              Outros: {
                label: "Outros",
                color: "hsl(var(--chart-5))",
              },
            }}
            className="min-h-64 w-full"
          >
            <PieChart data={categoryData} accessibilityLayer>
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    formatter={(value, name) => [
                      `R$ ${Number(value).toLocaleString()}`,
                      name
                    ]}
                  />
                }
              />
              <Pie
                data={categoryData.map((item, index) => ({
                  ...item,
                  fill: ["#4e6df5", "#39a561", "#e5a158", "#ef4444", "#8b5cf6"][index % 5]
                }))}
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={80}
                label={({ name, percentage }) => `${name}: ${percentage}%`}
              />
            </PieChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
}
