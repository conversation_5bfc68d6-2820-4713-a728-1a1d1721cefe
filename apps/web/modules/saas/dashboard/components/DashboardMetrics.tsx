"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import {
  DollarSign,
  ShoppingCart,
  Users,
  TrendingUp,
  TrendingDown,
  Minus,
  Package,
  CreditCard,
  Activity,
  Target,
  Zap,
  BarChart3,
} from "lucide-react";

interface MetricCardProps {
  title: string;
  value: string;
  change?: string;
  isPositive?: boolean;
  icon: React.ComponentType<{ className?: string }>;
  isLoading?: boolean;
  description?: string;
  badge?: {
    text: string;
    variant: "default" | "secondary" | "destructive" | "outline";
  };
}

function MetricCard({
  title,
  value,
  change,
  isPositive,
  icon: Icon,
  isLoading,
  description,
  badge
}: MetricCardProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          <Icon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-24 mb-2" />
          <Skeleton className="h-4 w-16" />
        </CardContent>
      </Card>
    );
  }

  const getChangeIcon = () => {
    if (!change) return <Minus className="h-3 w-3" />;
    return isPositive ? (
      <TrendingUp className="h-3 w-3" />
    ) : (
      <TrendingDown className="h-3 w-3" />
    );
  };

  const getChangeColor = () => {
    if (!change) return "text-muted-foreground";
    return isPositive ? "text-green-600" : "text-red-600";
  };

  return (
    <Card className="relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center gap-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          {badge && (
            <Badge variant={badge.variant} className="text-xs">
              {badge.text}
            </Badge>
          )}
        </div>
        <div className="relative">
          <Icon className="h-4 w-4 text-muted-foreground" />
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary/20 rounded-full animate-pulse" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-2xl font-bold">{value}</div>
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
          {change && (
            <div className={`flex items-center gap-1 text-xs ${getChangeColor()}`}>
              {getChangeIcon()}
              <span>{change}</span>
              <span className="text-muted-foreground">vs período anterior</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface DashboardMetricsProps {
  data?: {
    revenue: {
      total: number;
      change: number;
      monthly: number;
      monthlyChange: number;
    };
    sales: {
      total: number;
      change: number;
      today: number;
      todayChange: number;
    };
    customers: {
      total: number;
      change: number;
      active: number;
      activeChange: number;
    };
    products: {
      total: number;
      change: number;
      published: number;
      publishedChange: number;
    };
    conversion: {
      rate: number;
      change: number;
    };
    performance: {
      score: number;
      change: number;
    };
  };
  isLoading?: boolean;
}

export function DashboardMetrics({ data, isLoading }: DashboardMetricsProps) {
  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(cents / 100);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("pt-BR").format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  const metrics = [
    {
      title: "Receita Total",
      value: data ? formatCurrency(data.revenue.total) : "R$ 0,00",
      change: data ? `${data.revenue.change > 0 ? '+' : ''}${data.revenue.change.toFixed(1)}%` : undefined,
      isPositive: data ? data.revenue.change > 0 : true,
      icon: DollarSign,
      description: `Este mês: ${data ? formatCurrency(data.revenue.monthly) : 'R$ 0,00'}`,
      badge: {
        text: "Mês atual",
        variant: "secondary" as const,
      },
    },
    {
      title: "Vendas",
      value: data ? formatNumber(data.sales.total) : "0",
      change: data ? `${data.sales.change > 0 ? '+' : ''}${data.sales.change.toFixed(1)}%` : undefined,
      isPositive: data ? data.sales.change > 0 : true,
      icon: ShoppingCart,
      description: `Hoje: ${data ? formatNumber(data.sales.today) : '0'} vendas`,
      badge: {
        text: "Hoje",
        variant: "default" as const,
      },
    },
    {
      title: "Clientes",
      value: data ? formatNumber(data.customers.total) : "0",
      change: data ? `${data.customers.change > 0 ? '+' : ''}${data.customers.change.toFixed(1)}%` : undefined,
      isPositive: data ? data.customers.change > 0 : true,
      icon: Users,
      description: `${data ? formatNumber(data.customers.active) : '0'} ativos`,
      badge: {
        text: "Ativos",
        variant: "outline" as const,
      },
    },
    {
      title: "Produtos",
      value: data ? formatNumber(data.products.total) : "0",
      change: data ? `${data.products.change > 0 ? '+' : ''}${data.products.change.toFixed(1)}%` : undefined,
      isPositive: data ? data.products.change > 0 : true,
      icon: Package,
      description: `${data ? formatNumber(data.products.published) : '0'} publicados`,
      badge: {
        text: "Publicados",
        variant: "secondary" as const,
      },
    },
    {
      title: "Taxa de Conversão",
      value: data ? formatPercentage(data.conversion.rate) : "0%",
      change: data ? `${data.conversion.change > 0 ? '+' : ''}${data.conversion.change.toFixed(1)}%` : undefined,
      isPositive: data ? data.conversion.change > 0 : true,
      icon: Target,
      description: "Visitantes que compram",
      badge: {
        text: "Performance",
        variant: "default" as const,
      },
    },
    {
      title: "Score de Performance",
      value: data ? `${data.performance.score}/100` : "0/100",
      change: data ? `${data.performance.change > 0 ? '+' : ''}${data.performance.change.toFixed(1)}` : undefined,
      isPositive: data ? data.performance.change > 0 : true,
      icon: BarChart3,
      description: "Baseado em múltiplas métricas",
      badge: {
        text: "Score",
        variant: "outline" as const,
      },
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {metrics.map((metric, index) => (
        <MetricCard
          key={index}
          title={metric.title}
          value={metric.value}
          change={metric.change}
          isPositive={metric.isPositive}
          icon={metric.icon}
          isLoading={isLoading}
          description={metric.description}
          badge={metric.badge}
        />
      ))}
    </div>
  );
}
