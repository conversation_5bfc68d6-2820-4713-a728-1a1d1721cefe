"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { Skeleton } from "@ui/components/skeleton";
import { useDashboard } from "../hooks/useDashboard";
import { DashboardMetrics } from "./DashboardMetrics";
import { RecentActivity } from "./RecentActivity";
import { TopProducts } from "./TopProducts";
import {
  RevenueChart,
  TransactionsChart,
  PaymentMethodsChart,
} from "@saas/analytics/components/AnalyticsCharts";
import {
  BarChart3,
  TrendingUp,
  Users,
  CreditCard,
  Download,
  RefreshCw,
  Settings,
  Bell,
  Zap,
  Target,
  Activity,
} from "lucide-react";

interface DashboardClientProps {
  organization: {
    id: string;
    name: string;
    slug: string;
  };
}

export function DashboardClient({ organization }: DashboardClientProps) {
  const { data: dashboard, isLoading, error, refetch } = useDashboard(organization.id);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refetch();
    setIsRefreshing(false);
  };

  const handleExport = () => {
    // Implementar exportação
    console.log("Exporting dashboard data...");
  };

  if (error) {
    return (
      <div className="space-y-6 p-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-destructive/10 flex items-center justify-center">
            <Activity className="h-8 w-8 text-destructive" />
          </div>
          <h2 className="text-2xl font-bold text-destructive mb-2">
            Erro ao carregar dashboard
          </h2>
          <p className="text-muted-foreground mb-4">
            Não foi possível carregar os dados do dashboard. Tente novamente.
          </p>
          <Button onClick={handleRefresh} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Tentar novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Dashboard
          </h1>
          <p className="text-muted-foreground">
            Visão geral completa da sua plataforma
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configurações
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm font-medium">Performance</p>
                <p className="text-2xl font-bold">
                  {isLoading ? "..." : `${dashboard?.metrics.performance.score.toFixed(0) || 0}/100`}
                </p>
                <p className="text-blue-100 text-xs">
                  Score geral da plataforma
                </p>
              </div>
              <Target className="h-8 w-8 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm font-medium">Conversão</p>
                <p className="text-2xl font-bold">
                  {isLoading ? "..." : `${dashboard?.metrics.conversion.rate.toFixed(1) || 0}%`}
                </p>
                <p className="text-green-100 text-xs">
                  Taxa de conversão atual
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm font-medium">Atividade</p>
                <p className="text-2xl font-bold">
                  {isLoading ? "..." : dashboard?.activities.length || 0}
                </p>
                <p className="text-purple-100 text-xs">
                  Eventos nas últimas 24h
                </p>
              </div>
              <Activity className="h-8 w-8 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm font-medium">Crescimento</p>
                <p className="text-2xl font-bold">
                  {isLoading ? "..." : `+${dashboard?.metrics.sales.change.toFixed(1) || 0}%`}
                </p>
                <p className="text-orange-100 text-xs">
                  Crescimento mensal
                </p>
              </div>
              <Zap className="h-8 w-8 text-orange-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Metrics */}
      <DashboardMetrics data={dashboard?.metrics} isLoading={isLoading} />

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="sales">Vendas</TabsTrigger>

        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {isLoading ? (
              <>
                <Skeleton className="h-[400px] w-full" />
                <Skeleton className="h-[400px] w-full" />
              </>
            ) : (
              <>
                <RevenueChart data={dashboard?.charts.revenueChart || []} />
                <TransactionsChart data={dashboard?.charts.revenueChart || []} />
              </>
            )}
          </div>


        </TabsContent>

        <TabsContent value="sales" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {isLoading ? (
              <>
                <Skeleton className="h-[400px] w-full" />
                <Skeleton className="h-[400px] w-full" />
              </>
            ) : (
              <>
                <RevenueChart data={dashboard?.charts.revenueChart || []} />
                <PaymentMethodsChart data={[]} />
              </>
            )}
          </div>

          {/* <RecentActivity
            activities={dashboard?.activities || []}
            isLoading={isLoading}
            maxItems={20}
          /> */}
        </TabsContent>

        {/* <TabsContent value="products" className="space-y-4">
          <TopProducts
            products={dashboard?.topProducts || []}
            isLoading={isLoading}
            maxItems={10}
          />

          <div className="grid gap-4 md:grid-cols-2">
            {isLoading ? (
              <>
                <Skeleton className="h-[400px] w-full" />
                <Skeleton className="h-[400px] w-full" />
              </>
            ) : (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle>Produtos por Status</CardTitle>
                    <CardDescription>
                      Distribuição dos seus produtos
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm">Publicados</span>
                        </div>
                        <span className="font-semibold">
                          {dashboard?.metrics.products.published || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                          <span className="text-sm">Rascunhos</span>
                        </div>
                        <span className="font-semibold">
                          {(dashboard?.metrics.products.total || 0) - (dashboard?.metrics.products.published || 0)}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Performance dos Produtos</CardTitle>
                    <CardDescription>
                      Métricas de vendas por produto
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {dashboard?.topProducts.slice(0, 3).map((product, index) => (
                        <div key={product.id} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-bold">
                              {index + 1}
                            </div>
                            <span className="text-sm font-medium truncate">{product.name}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-semibold">
                              {product.sales} vendas
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {((product.revenue / 100).toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Total de Clientes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {isLoading ? "..." : dashboard?.metrics.customers.total || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  Clientes únicos que compraram
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Clientes Ativos
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {isLoading ? "..." : dashboard?.metrics.customers.active || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  Últimos 30 dias
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Taxa de Retenção
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {isLoading ? "..." : "85%"}
                </div>
                <p className="text-sm text-muted-foreground">
                  Clientes que retornam
                </p>
              </CardContent>
            </Card>
          </div>

          <RecentActivity
            activities={dashboard?.activities || []}
            isLoading={isLoading}
            maxItems={15}
          />
        </TabsContent> */}
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Ações Rápidas</CardTitle>
          <CardDescription>
            Acesse rapidamente as funcionalidades mais importantes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <BarChart3 className="h-6 w-6" />
              <span className="text-sm">Analytics</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <CreditCard className="h-6 w-6" />
              <span className="text-sm">Pagamentos</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <Users className="h-6 w-6" />
              <span className="text-sm">Clientes</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col gap-2">
              <Settings className="h-6 w-6" />
              <span className="text-sm">Configurações</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
