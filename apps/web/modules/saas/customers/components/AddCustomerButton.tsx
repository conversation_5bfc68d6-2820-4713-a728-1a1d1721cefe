"use client";

import { But<PERSON> } from "@ui/components/button";
import { UsersIcon } from "lucide-react";
import { useCustomerForm } from "../hooks/useCustomerForm";

interface AddCustomerButtonProps {
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  children?: React.ReactNode;
}

export function AddCustomerButton({
  variant = "default",
  size = "default",
  className,
  children
}: AddCustomerButtonProps) {
  const { openCreateForm } = useCustomerForm();

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={openCreateForm}
    >
      {children || (
        <>
          <UsersIcon className="h-4 w-4 mr-2" />
          Adicionar <PERSON>liente
        </>
      )}
    </Button>
  );
}
