"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetDescription } from "@ui/components/sheet";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
  UserIcon,
  MailIcon,
  PhoneIcon,
  MapPinIcon,
  SaveIcon,
  XIcon,
  AlertCircleIcon,
  CheckCircleIcon
} from "lucide-react";

interface Customer {
  id?: string;
  name: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  status: "active" | "inactive" | "pending" | "vip";
  avatar?: string;
  tags: string[];
  notes?: string;
  birthDate?: string;
  document?: string;
  address?: string;
  zipCode?: string;
  company?: string;
  position?: string;
}

interface SimpleCustomerFormSheetProps {
  isOpen: boolean;
  onClose: () => void;
  customer?: Customer | null;
  onSave: (customer: Customer) => void;
  mode: 'create' | 'edit';
}

const statusOptions = [
  { id: 'active', name: 'Ativo', color: 'text-green-600', bgColor: 'bg-green-50' },
  { id: 'inactive', name: 'Inativo', color: 'text-red-600', bgColor: 'bg-red-50' },
  { id: 'pending', name: 'Pendente', color: 'text-yellow-600', bgColor: 'bg-yellow-50' },
  { id: 'vip', name: 'VIP', color: 'text-purple-600', bgColor: 'bg-purple-50' },
];

const states = [
  'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA',
  'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN',
  'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
];

export function SimpleCustomerFormSheet({ isOpen, onClose, customer, onSave, mode }: SimpleCustomerFormSheetProps) {
  const [formData, setFormData] = useState<Customer>({
    name: '',
    email: '',
    phone: '',
    city: '',
    state: '',
    status: 'active',
    tags: [],
    notes: '',
    birthDate: '',
    document: '',
    address: '',
    zipCode: '',
    company: '',
    position: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Preencher formulário quando editar
  useEffect(() => {
    if (customer && mode === 'edit') {
      setFormData(customer);
      setShowAdvanced(true); // Mostrar campos avançados na edição
    } else {
      setFormData({
        name: '',
        email: '',
        phone: '',
        city: '',
        state: '',
        status: 'active',
        tags: [],
        notes: '',
        birthDate: '',
        document: '',
        address: '',
        zipCode: '',
        company: '',
        position: '',
      });
      setShowAdvanced(false);
    }
    setErrors({});
  }, [customer, mode, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nome é obrigatório';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Telefone é obrigatório';
    } else if (!/^\(\d{2}\)\s\d{4,5}-\d{4}$/.test(formData.phone)) {
      newErrors.phone = 'Formato: (11) 99999-9999';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'Cidade é obrigatória';
    }

    if (!formData.state) {
      newErrors.state = 'Estado é obrigatório';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof Customer, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePhoneChange = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 11) {
      const formatted = numbers.replace(/(\d{2})(\d{4,5})(\d{4})/, '($1) $2-$3');
      handleInputChange('phone', formatted);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      onSave(formData);
      onClose();
    } catch (error) {
      console.error('Erro ao salvar cliente:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[500px] sm:w-[600px] overflow-y-auto">
        <SheetHeader className="sticky top-0 bg-background z-10 pb-4 border-b">
          <SheetTitle className="flex items-center gap-2">
            <UserIcon className="h-5 w-5" />
            {mode === 'create' ? 'Novo Cliente' : 'Editar Cliente'}
          </SheetTitle>
          <SheetDescription>
            {mode === 'create'
              ? 'Preencha os dados básicos para criar um novo cliente'
              : 'Atualize as informações do cliente'
            }
          </SheetDescription>
        </SheetHeader>

        <form onSubmit={handleSubmit} className="space-y-6 mt-6 pb-20">
          {/* Informações Básicas - Sempre visíveis */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Informações Básicas</CardTitle>
              <CardDescription>Dados essenciais do cliente</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Avatar e Nome */}
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={formData.avatar} />
                  <AvatarFallback className="bg-primary/10 text-primary text-lg font-semibold">
                    {formData.name.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <Label htmlFor="name" className="text-sm font-medium">Nome Completo *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Digite o nome completo"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                      <AlertCircleIcon className="h-3 w-3" />
                      {errors.name}
                    </p>
                  )}
                </div>
              </div>

              {/* Email e Telefone */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email" className="text-sm font-medium">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                      <AlertCircleIcon className="h-3 w-3" />
                      {errors.email}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="phone" className="text-sm font-medium">Telefone *</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handlePhoneChange(e.target.value)}
                    placeholder="(11) 99999-9999"
                    className={errors.phone ? 'border-red-500' : ''}
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                      <AlertCircleIcon className="h-3 w-3" />
                      {errors.phone}
                    </p>
                  )}
                </div>
              </div>

              {/* Localização */}
              <div className="grid grid-cols-3 gap-4">
                <div className="col-span-2">
                  <Label htmlFor="city" className="text-sm font-medium">Cidade *</Label>
                  <Input
                    id="city"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="São Paulo"
                    className={errors.city ? 'border-red-500' : ''}
                  />
                  {errors.city && (
                    <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                      <AlertCircleIcon className="h-3 w-3" />
                      {errors.city}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="state" className="text-sm font-medium">Estado *</Label>
                  <Select value={formData.state} onValueChange={(value) => handleInputChange('state', value)}>
                    <SelectTrigger className={errors.state ? 'border-red-500' : ''}>
                      <SelectValue placeholder="UF" />
                    </SelectTrigger>
                    <SelectContent>
                      {states.map((state) => (
                        <SelectItem key={state} value={state}>{state}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.state && (
                    <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                      <AlertCircleIcon className="h-3 w-3" />
                      {errors.state}
                    </p>
                  )}
                </div>
              </div>

              {/* Status */}
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <div className="flex gap-2 mt-2">
                  {statusOptions.map((option) => (
                    <Button
                      key={option.id}
                      type="button"
                      variant={formData.status === option.id ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleInputChange('status', option.id)}
                      className={`${formData.status === option.id ? option.bgColor : ''}`}
                    >
                      {option.name}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Campos Avançados - Opcionais */}
          {showAdvanced && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <MapPinIcon className="h-4 w-4" />
                  Informações Adicionais
                </CardTitle>
                <CardDescription>Campos opcionais para mais detalhes</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="address" className="text-sm font-medium">Endereço</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    placeholder="Rua, número, bairro"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="zipCode" className="text-sm font-medium">CEP</Label>
                    <Input
                      id="zipCode"
                      value={formData.zipCode}
                      onChange={(e) => handleInputChange('zipCode', e.target.value)}
                      placeholder="00000-000"
                    />
                  </div>
                  <div>
                    <Label htmlFor="document" className="text-sm font-medium">CPF/CNPJ</Label>
                    <Input
                      id="document"
                      value={formData.document}
                      onChange={(e) => handleInputChange('document', e.target.value)}
                      placeholder="000.000.000-00"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="company" className="text-sm font-medium">Empresa</Label>
                    <Input
                      id="company"
                      value={formData.company}
                      onChange={(e) => handleInputChange('company', e.target.value)}
                      placeholder="Nome da empresa"
                    />
                  </div>
                  <div>
                    <Label htmlFor="position" className="text-sm font-medium">Cargo</Label>
                    <Input
                      id="position"
                      value={formData.position}
                      onChange={(e) => handleInputChange('position', e.target.value)}
                      placeholder="Cargo na empresa"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="birthDate" className="text-sm font-medium">Data de Nascimento</Label>
                  <Input
                    id="birthDate"
                    type="date"
                    value={formData.birthDate}
                    onChange={(e) => handleInputChange('birthDate', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="notes" className="text-sm font-medium">Observações</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="Observações sobre o cliente..."
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Toggle para campos avançados */}
          {!showAdvanced && (
            <div className="text-center">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAdvanced(true)}
                className="w-full"
              >
                + Adicionar informações extras (opcional)
              </Button>
            </div>
          )}

          {/* Ações */}
          <div className="flex gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              <XIcon className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Salvando...
                </>
              ) : (
                <>
                  <SaveIcon className="h-4 w-4 mr-2" />
                  {mode === 'create' ? 'Criar Cliente' : 'Salvar Alterações'}
                </>
              )}
            </Button>
          </div>
        </form>
      </SheetContent>
    </Sheet>
  );
}
