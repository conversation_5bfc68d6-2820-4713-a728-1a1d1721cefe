"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Card, CardContent } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
  MailIcon,
  TargetIcon,
  UsersIcon,
  MessageSquareIcon,
  CalendarIcon,
  CheckCircleIcon,
  AlertCircleIcon
} from "lucide-react";

interface CustomerActionsModalProps {
  children: React.ReactNode;
  selectedCustomers?: number;
}

export function CustomerActionsModal({ children, selectedCustomers = 0 }: CustomerActionsModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [action, setAction] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const handleAction = async () => {
    setIsProcessing(true);
    // Simular processamento
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsProcessing(false);
    setIsOpen(false);
    // Aqui seria feita a chamada para a API
  };

  const getActionConfig = (actionType: string) => {
    const configs = {
      email: {
        title: "Email Marketing",
        description: "Envie emails personalizados para seus clientes",
        icon: MailIcon,
        color: "text-blue-600",
        bgColor: "bg-blue-50",
        borderColor: "border-blue-200"
      },
      segment: {
        title: "Segmentação",
        description: "Crie segmentos personalizados de clientes",
        icon: TargetIcon,
        color: "text-green-600",
        bgColor: "bg-green-50",
        borderColor: "border-green-200"
      },
      followup: {
        title: "Follow-up",
        description: "Agende follow-ups automáticos",
        icon: MessageSquareIcon,
        color: "text-purple-600",
        bgColor: "bg-purple-50",
        borderColor: "border-purple-200"
      }
    };
    return configs[actionType as keyof typeof configs] || configs.email;
  };

  const config = getActionConfig(action);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UsersIcon className="h-5 w-5" />
            Ações com Clientes
          </DialogTitle>
          <DialogDescription>
            {selectedCustomers > 0
              ? `Ações para ${selectedCustomers} cliente(s) selecionado(s)`
              : "Escolha uma ação para seus clientes"
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Seleção de Ação */}
          <div className="space-y-4">
            <Label>Escolha uma ação</Label>
            <div className="grid gap-3">
              <Card
                className={`cursor-pointer transition-all ${
                  action === 'email' ? `${config.bgColor} ${config.borderColor} border-2` : 'hover:bg-muted/50'
                }`}
                onClick={() => setAction('email')}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <MailIcon className={`h-5 w-5 ${config.color}`} />
                    <div>
                      <p className="font-medium">Email Marketing</p>
                      <p className="text-sm text-muted-foreground">Envie campanhas personalizadas</p>
                    </div>
                    {action === 'email' && <CheckCircleIcon className="h-4 w-4 text-green-600 ml-auto" />}
                  </div>
                </CardContent>
              </Card>

              <Card
                className={`cursor-pointer transition-all ${
                  action === 'segment' ? `${config.bgColor} ${config.borderColor} border-2` : 'hover:bg-muted/50'
                }`}
                onClick={() => setAction('segment')}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <TargetIcon className={`h-5 w-5 ${config.color}`} />
                    <div>
                      <p className="font-medium">Segmentação</p>
                      <p className="text-sm text-muted-foreground">Crie grupos de clientes</p>
                    </div>
                    {action === 'segment' && <CheckCircleIcon className="h-4 w-4 text-green-600 ml-auto" />}
                  </div>
                </CardContent>
              </Card>

              <Card
                className={`cursor-pointer transition-all ${
                  action === 'followup' ? `${config.bgColor} ${config.borderColor} border-2` : 'hover:bg-muted/50'
                }`}
                onClick={() => setAction('followup')}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <MessageSquareIcon className={`h-5 w-5 ${config.color}`} />
                    <div>
                      <p className="font-medium">Follow-up</p>
                      <p className="text-sm text-muted-foreground">Agende contatos automáticos</p>
                    </div>
                    {action === 'followup' && <CheckCircleIcon className="h-4 w-4 text-green-600 ml-auto" />}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Configurações da Ação */}
          {action && (
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <AlertCircleIcon className="h-4 w-4" />
                <span>Configure os detalhes da ação</span>
              </div>

              {action === 'email' && (
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="subject">Assunto do Email</Label>
                    <Input id="subject" placeholder="Ex: Oferta especial para você!" />
                  </div>
                  <div>
                    <Label htmlFor="template">Template</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um template" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="welcome">Boas-vindas</SelectItem>
                        <SelectItem value="promotion">Promoção</SelectItem>
                        <SelectItem value="followup">Follow-up</SelectItem>
                        <SelectItem value="custom">Personalizado</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {action === 'segment' && (
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="segmentName">Nome do Segmento</Label>
                    <Input id="segmentName" placeholder="Ex: Clientes VIP" />
                  </div>
                  <div>
                    <Label htmlFor="criteria">Critérios</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione critérios" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high-value">Alto valor gasto</SelectItem>
                        <SelectItem value="recent">Compra recente</SelectItem>
                        <SelectItem value="inactive">Inativos há 30+ dias</SelectItem>
                        <SelectItem value="location">Por localização</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {action === 'followup' && (
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="followupType">Tipo de Follow-up</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="sms">SMS</SelectItem>
                        <SelectItem value="whatsapp">WhatsApp</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="schedule">Agendar para</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Quando enviar" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="immediate">Agora</SelectItem>
                        <SelectItem value="tomorrow">Amanhã</SelectItem>
                        <SelectItem value="week">Próxima semana</SelectItem>
                        <SelectItem value="custom">Data personalizada</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Resumo da Ação */}
          {action && (
            <Card className="bg-muted/50">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <config.icon className={`h-4 w-4 ${config.color}`} />
                  <span className="font-medium">{config.title}</span>
                </div>
                <p className="text-sm text-muted-foreground">{config.description}</p>
                {selectedCustomers > 0 && (
                  <Badge variant="secondary" className="mt-2">
                    {selectedCustomers} cliente(s) selecionado(s)
                  </Badge>
                )}
              </CardContent>
            </Card>
          )}

          {/* Ações */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleAction}
              disabled={!action || isProcessing}
              className="flex-1"
            >
              {isProcessing ? "Processando..." : "Executar Ação"}
            </Button>
          </div>

          {/* Informações Adicionais */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p>• As ações serão aplicadas aos clientes selecionados</p>
            <p>• Você receberá um relatório de execução</p>
            <p>• Pode cancelar a ação até 5 minutos após o envio</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
