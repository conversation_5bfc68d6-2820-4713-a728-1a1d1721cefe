"use client";

import { useState } from "react";
import { DataTable, DataTableColumn, DataTableAction } from "@saas/shared/components/DataTable";
import { ActionBar } from "@saas/shared/components/ActionBar";
import { AdvancedFiltersSheet, FilterConfig } from "@saas/shared/components/AdvancedFiltersSheet";
import { useAdvancedFilters } from "@saas/shared/hooks/useAdvancedFilters";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { CustomerDetailsSheet } from "./CustomerDetailsSheet";
import { useCustomerForm } from "../hooks/useCustomerForm";
import {
  FilterIcon,
  DownloadIcon,
  MailIcon,
  EyeIcon,
  EditIcon,
  Trash2Icon,
  UserPlusIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  StarIcon,
  DollarSignIcon,
  MapPinIcon,
  CalendarIcon,
} from "lucide-react";

interface CustomersTableProps {
  organizationId: string;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: "active" | "inactive" | "pending" | "verified" | "vip";
  totalSpent: number;
  lastPurchase: string;
  createdAt: string;
  city: string;
  state: string;
  avatar?: string;
  purchases: number;
  averageOrderValue: number;
  joinDate: string;
  tags: string[];
  notes?: string;
}

// Dados mockados para demonstração
const mockCustomers: Customer[] = [
  {
    id: "1",
    name: "João Silva",
    email: "<EMAIL>",
    phone: "(11) 99999-9999",
    status: "vip",
    totalSpent: 2500.00,
    lastPurchase: "2024-01-20",
    createdAt: "2023-03-15",
    city: "São Paulo",
    state: "SP",
    purchases: 8,
    averageOrderValue: 312.50,
    joinDate: "2023-03-15",
    tags: ["VIP", "Frequente"],
    notes: "Cliente muito satisfeito, sempre compra em promoções"
  },
  {
    id: "2",
    name: "Maria Santos",
    email: "<EMAIL>",
    phone: "(21) 88888-8888",
    status: "active",
    totalSpent: 1800.00,
    lastPurchase: "2024-01-18",
    createdAt: "2023-05-22",
    city: "Rio de Janeiro",
    state: "RJ",
  },
  {
    id: "3",
    name: "Pedro Costa",
    email: "<EMAIL>",
    phone: "(31) 77777-7777",
    status: "pending",
    totalSpent: 950.00,
    lastPurchase: "2024-01-15",
    createdAt: "2023-08-10",
    city: "Belo Horizonte",
    state: "MG",
  },
  {
    id: "4",
    name: "Ana Oliveira",
    email: "<EMAIL>",
    phone: "(41) 66666-6666",
    status: "inactive",
    totalSpent: 0.00,
    lastPurchase: "",
    createdAt: "2023-12-01",
    city: "Curitiba",
    state: "PR",
  },
];

const statusColors = {
  verified: "bg-green-100 text-green-800 border-green-200",
  active: "bg-blue-100 text-blue-800 border-blue-200",
  pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
  inactive: "bg-gray-100 text-gray-800 border-gray-200",
};

const statusLabels = {
  verified: "Verificado",
  active: "Ativo",
  pending: "Pendente",
  inactive: "Inativo",
};

export function CustomersTable({ organizationId }: CustomersTableProps) {
  const [selectedCustomers, setSelectedCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { openEditForm } = useCustomerForm();

  const { filters, updateFilters, clearFilters, activeFiltersCount } = useAdvancedFilters({
    initialFilters: {
      searchTerm: "",
      status: [],
      totalSpentRange: { min: null, max: null },
      location: "",
      joinDateRange: { min: null, max: null },
    },
    onFiltersChange: (newFilters) => {
      // Aqui você pode implementar a lógica de filtros
      console.log("Filtros aplicados:", newFilters);
    }
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Nunca";
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  // Configuração dos filtros
  const filterConfigs: FilterConfig[] = [
    {
      type: 'checkbox',
      key: 'status',
      label: 'Status',
      icon: CheckCircleIcon,
      options: [
        { id: 'active', name: 'Ativo', icon: CheckCircleIcon, color: 'text-green-600', count: 892 },
        { id: 'inactive', name: 'Inativo', icon: XCircleIcon, color: 'text-red-600', count: 156 },
        { id: 'pending', name: 'Pendente', icon: ClockIcon, color: 'text-yellow-600', count: 144 },
        { id: 'vip', name: 'VIP', icon: StarIcon, color: 'text-purple-600', count: 89 },
      ]
    },
    {
      type: 'range',
      key: 'totalSpentRange',
      label: 'Valor Total Gasto',
      icon: DollarSignIcon,
      min: 0,
      max: 10000,
      step: 100
    },
    {
      type: 'input',
      key: 'location',
      label: 'Localização',
      icon: MapPinIcon,
      placeholder: 'Cidade ou estado'
    },
    {
      type: 'range',
      key: 'joinDateRange',
      label: 'Data de Cadastro',
      icon: CalendarIcon,
      min: 2020,
      max: 2024,
      step: 1
    }
  ];

  const filteredCustomers = mockCustomers.filter(customer => {
    // Filtro por termo de busca
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      if (!customer.name.toLowerCase().includes(searchTerm) &&
          !customer.email.toLowerCase().includes(searchTerm) &&
          !customer.phone.includes(searchTerm)) {
        return false;
      }
    }

    // Filtro por status
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(customer.status)) {
        return false;
      }
    }

    // Filtro por valor total gasto
    if (filters.totalSpentRange) {
      const { min, max } = filters.totalSpentRange;
      if (min !== null && customer.totalSpent < min) return false;
      if (max !== null && customer.totalSpent > max) return false;
    }

    // Filtro por localização
    if (filters.location) {
      const location = filters.location.toLowerCase();
      if (!customer.city.toLowerCase().includes(location) &&
          !customer.state.toLowerCase().includes(location)) {
        return false;
      }
    }

    // Filtro por data de cadastro
    if (filters.joinDateRange) {
      const { min, max } = filters.joinDateRange;
      const joinYear = new Date(customer.joinDate).getFullYear();
      if (min !== null && joinYear < min) return false;
      if (max !== null && joinYear > max) return false;
    }

    return true;
  });

  const columns: DataTableColumn<Customer>[] = [
    {
      key: "name",
      label: "Cliente",
      render: (_, customer) => (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={customer.avatar} />
            <AvatarFallback className="bg-primary/10 text-primary text-xs">
              {customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium text-sm">{customer.name}</p>
            <p className="text-xs text-muted-foreground">{customer.email}</p>
          </div>
        </div>
      ),
      width: "300px",
    },
    {
      key: "phone",
      label: "Telefone",
      render: (phone) => (
        <span className="text-sm">{phone}</span>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (status) => (
        <Badge
          variant="outline"
          className={`${statusColors[status as keyof typeof statusColors]} text-xs`}
        >
          {statusLabels[status as keyof typeof statusLabels]}
        </Badge>
      ),
    },
    {
      key: "totalSpent",
      label: "Total Gasto",
      render: (totalSpent) => (
        <span className="font-medium text-sm">
          {formatCurrency(totalSpent)}
        </span>
      ),
    },
    {
      key: "lastPurchase",
      label: "Última Compra",
      render: (lastPurchase) => (
        <span className="text-sm text-muted-foreground">
          {formatDate(lastPurchase)}
        </span>
      ),
    },
    {
      key: "city",
      label: "Localização",
      render: (_, customer) => (
        <span className="text-sm">
          {customer.city}, {customer.state}
        </span>
      ),
    },
  ];

  const actions: DataTableAction<Customer>[] = [
    {
      label: "Ver Detalhes",
      onClick: (customer) => {
        setSelectedCustomer(customer);
        setIsSheetOpen(true);
      },
    },
    {
      label: "Editar",
      onClick: (customer) => openEditForm(customer),
    },
    {
      label: "Excluir",
      onClick: (customer) => console.log("Excluir", customer),
      variant: "destructive",
    },
  ];

  return (
    <div className="space-y-6">
      <ActionBar
        searchValue={filters.searchTerm || ""}
        onSearchChange={(value) => updateFilters({ ...filters, searchTerm: value })}
        searchPlaceholder="Buscar clientes por nome, email ou telefone..."
        selectedCount={selectedCustomers.length}
        onClearSelection={() => setSelectedCustomers([])}
        actions={
          <>
            <AdvancedFiltersSheet
              onFiltersChange={updateFilters}
              activeFilters={filters}
              filterConfigs={filterConfigs}
              totalItems={filteredCustomers.length}
              title="Filtros de Clientes"
              description="Aplique filtros para encontrar clientes específicos"
              triggerLabel="Filtros"
              searchPlaceholder="Buscar clientes..."
              onClearFilters={clearFilters}
            />
            <Button>
              <UserPlusIcon className="h-4 w-4 mr-2" />
              Novo Cliente
            </Button>
          </>
        }
        bulkActions={
          <>
            <Button variant="outline" size="sm">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar Selecionados
            </Button>
            <Button variant="outline" size="sm">
              <MailIcon className="h-4 w-4 mr-2" />
              Enviar Email
            </Button>
          </>
        }
      />

      <DataTable
        data={filteredCustomers}
        columns={columns}
        actions={actions}
        selectable
        onSelectionChange={setSelectedCustomers}
        emptyMessage="Nenhum cliente encontrado"
      />

      {/* Sheet de detalhes do cliente */}
      <CustomerDetailsSheet
        customer={selectedCustomer}
        isOpen={isSheetOpen}
        onClose={() => {
          setIsSheetOpen(false);
          setSelectedCustomer(null);
        }}
      />
    </div>
  );
}
