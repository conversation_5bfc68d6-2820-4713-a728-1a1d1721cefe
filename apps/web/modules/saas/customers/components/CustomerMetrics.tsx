"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import {
  UsersIcon,
  TrendingUpIcon,
  DollarSignIcon,
  TargetIcon,
  HeartIcon,
  ShoppingCartIcon,
} from "lucide-react";

interface CustomerMetricsProps {
  organizationId: string;
}

interface MetricsData {
  total: number;
  active: number;
  totalRevenue: number;
  averageOrderValue: number;
  monthlyGrowth: number;
  retentionRate: number;
  conversionRate: number;
  lifetimeValue: number;
}

// Dados mockados para demonstração
const mockMetrics: MetricsData = {
  total: 1371,
  active: 892,
  totalRevenue: 1250000,
  averageOrderValue: 450,
  monthlyGrowth: 15.2,
  retentionRate: 89.3,
  conversionRate: 3.2,
  lifetimeValue: 1250,
};

export function CustomerMetrics({ organizationId }: CustomerMetricsProps) {
  // Aqui você faria a chamada real para a API
  const metrics = mockMetrics;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value}%`;
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total de Clientes */}
      <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white relative overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm font-medium">Total de Clientes</p>
              <p className="text-3xl font-bold">
                {metrics.total.toLocaleString('pt-BR')}
              </p>
              <p className="text-blue-100 text-xs">
                {formatPercentage(metrics.monthlyGrowth)} crescimento mensal
              </p>
            </div>
            <UsersIcon className="h-8 w-8 text-blue-200" />
          </div>
        </CardContent>
      </Card>

      {/* Clientes Ativos */}
      <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white relative overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm font-medium">Clientes Ativos</p>
              <p className="text-3xl font-bold">
                {metrics.active.toLocaleString('pt-BR')}
              </p>
              <p className="text-green-100 text-xs">
                {((metrics.active / metrics.total) * 100).toFixed(1)}% do total
              </p>
            </div>
            <TrendingUpIcon className="h-8 w-8 text-green-200" />
          </div>
        </CardContent>
      </Card>

      {/* Taxa de Conversão */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">Taxa de Conversão</CardTitle>
          <TargetIcon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {metrics.conversionRate}%
          </div>
          <div className="flex items-center gap-1 text-xs text-green-600">
            <TrendingUpIcon className="h-3 w-3" />
            <span>Visitantes que compram</span>
          </div>
        </CardContent>
      </Card>

      {/* Valor Médio do Pedido */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">Ticket Médio</CardTitle>
          <ShoppingCartIcon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-purple-600">
            {formatCurrency(metrics.averageOrderValue)}
          </div>
          <p className="text-xs text-muted-foreground">
            Por pedido
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
