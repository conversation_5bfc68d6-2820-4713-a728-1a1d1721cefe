"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>it<PERSON> } from "@ui/components/sheet";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Separator } from "@ui/components/separator";
import {
  MailIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  DollarSignIcon,
  ShoppingCartIcon,
  TrendingUpIcon,
  MessageSquareIcon,
  StarIcon,
  MoreHorizontalIcon,
  EditIcon,
  TrashIcon,
  SendIcon
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  city: string;
  state: string;
  status: "active" | "inactive" | "pending" | "vip";
  totalSpent: number;
  lastPurchase: string;
  purchases: number;
  averageOrderValue: number;
  joinDate: string;
  tags: string[];
  notes?: string;
}

interface CustomerDetailsSheetProps {
  customer: Customer | null;
  isOpen: boolean;
  onClose: () => void;
}

export function CustomerDetailsSheet({ customer, isOpen, onClose }: CustomerDetailsSheetProps) {
  const [isEditing, setIsEditing] = useState(false);

  if (!customer) return null;

  // Garantir que os dados tenham valores padrão seguros
  const safeCustomer = {
    ...customer,
    tags: customer.tags || [],
    notes: customer.notes || '',
    totalSpent: customer.totalSpent || 0,
    purchases: customer.purchases || 0,
    averageOrderValue: customer.averageOrderValue || 0,
    lastPurchase: customer.lastPurchase || customer.joinDate || new Date().toISOString(),
    joinDate: customer.joinDate || new Date().toISOString(),
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusConfig = (status: string) => {
    const configs = {
      active: { label: "Ativo", variant: "default" as const, color: "text-green-600" },
      inactive: { label: "Inativo", variant: "secondary" as const, color: "text-gray-600" },
      pending: { label: "Pendente", variant: "outline" as const, color: "text-yellow-600" },
      vip: { label: "VIP", variant: "destructive" as const, color: "text-purple-600" },
    };
    return configs[status as keyof typeof configs] || configs.active;
  };

  const statusConfig = getStatusConfig(safeCustomer.status);

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[40%] min-w-[500px] p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <SheetHeader className="p-6 pb-4 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={safeCustomer.avatar} />
                  <AvatarFallback className="bg-primary/10 text-primary text-lg font-semibold">
                    {safeCustomer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <SheetTitle className="text-xl">{safeCustomer.name}</SheetTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant={statusConfig.variant} className={statusConfig.color}>
                      {statusConfig.label}
                    </Badge>
                    {safeCustomer.tags?.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    )) || []}
                  </div>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontalIcon className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setIsEditing(!isEditing)}>
                    <EditIcon className="h-4 w-4 mr-2" />
                    {isEditing ? "Cancelar Edição" : "Editar"}
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <SendIcon className="h-4 w-4 mr-2" />
                    Enviar Email
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <MessageSquareIcon className="h-4 w-4 mr-2" />
                    WhatsApp
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600">
                    <TrashIcon className="h-4 w-4 mr-2" />
                    Excluir
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6 space-y-6">
            {/* Contact Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Informações de Contato</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3">
                  <MailIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{safeCustomer.email}</span>
                </div>
                <div className="flex items-center gap-3">
                  <PhoneIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{safeCustomer.phone}</span>
                </div>
                <div className="flex items-center gap-3">
                  <MapPinIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{safeCustomer.city}, {safeCustomer.state}</span>
                </div>
                <div className="flex items-center gap-3">
                  <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Cliente desde {formatDate(safeCustomer.joinDate)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Financial Summary */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Resumo Financeiro</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {formatCurrency(safeCustomer.totalSpent)}
                    </div>
                    <div className="text-xs text-muted-foreground">Total Gasto</div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {safeCustomer.purchases}
                    </div>
                    <div className="text-xs text-muted-foreground">Compras</div>
                  </div>
                </div>
                <div className="mt-4 text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-lg font-semibold text-purple-600">
                    {formatCurrency(safeCustomer.averageOrderValue)}
                  </div>
                  <div className="text-xs text-muted-foreground">Ticket Médio</div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Atividade Recente</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3 p-2 hover:bg-muted/50 rounded">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Última compra</p>
                    <p className="text-xs text-muted-foreground">{formatDate(safeCustomer.lastPurchase)}</p>
                  </div>
                  <span className="text-sm font-medium text-green-600">
                    {formatCurrency(safeCustomer.averageOrderValue)}
                  </span>
                </div>
                <div className="flex items-center gap-3 p-2 hover:bg-muted/50 rounded">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Cadastro realizado</p>
                    <p className="text-xs text-muted-foreground">{formatDate(safeCustomer.joinDate)}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-2 hover:bg-muted/50 rounded">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Status atualizado</p>
                    <p className="text-xs text-muted-foreground">Há 2 dias</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Notes */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Observações</CardTitle>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <textarea
                    className="w-full p-3 border rounded-lg resize-none"
                    rows={3}
                    placeholder="Adicione observações sobre o cliente..."
                    defaultValue={safeCustomer.notes || ""}
                  />
                ) : (
                  <p className="text-sm text-muted-foreground">
                    {safeCustomer.notes || "Nenhuma observação adicionada."}
                  </p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Footer Actions */}
          <div className="p-6 pt-4 border-t bg-muted/30">
            <div className="flex gap-3">
              <Button variant="outline" className="flex-1">
                <MessageSquareIcon className="h-4 w-4 mr-2" />
                Contatar
              </Button>
              <Button variant="outline" className="flex-1">
                <MailIcon className="h-4 w-4 mr-2" />
                Email
              </Button>
              <Button className="flex-1">
                <EditIcon className="h-4 w-4 mr-2" />
                Editar
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
