"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import {
  PackageIcon,
  CreditCardIcon,
  SettingsIcon,
  BarChart3Icon,
  TrendingUpIcon,
  TagIcon,
  UsersIcon,
  HandshakeIcon,
  ArrowRightIcon,
  CheckIcon,
  XIcon,
} from "lucide-react";

// Demo component to showcase the layout optimization improvements
export function LayoutComparisonDemo() {
  const [activeView, setActiveView] = useState<"before" | "after">("before");

  const sidebarItems = [
    { id: "ofertas", label: "Ofertas", icon: PackageIcon },
    { id: "checkouts", label: "Checkouts", icon: CreditCardIcon },
    { id: "configuracoes", label: "Configurações", icon: SettingsIcon },
    { id: "pixels", label: "Pixels de rastreamento", icon: BarChart3Icon },
    { id: "upsell", label: "Upsell, downsell e mais", icon: TrendingUpIcon },
    { id: "cupons", label: "Cupons", icon: TagIcon },
    { id: "afiliacao", label: "Afiliação", icon: UsersIcon },
    { id: "coproducao", label: "Coprodução", icon: HandshakeIcon },
  ];

  const improvements = [
    {
      aspect: "Sidebar Padding",
      before: "p-6 (24px all sides)",
      after: "px-4 py-6 (16px horizontal)",
      improvement: "33% reduction in horizontal padding",
      impact: "positive",
    },
    {
      aspect: "Content Area Width",
      before: "Constrained by AppWrapper",
      after: "Full viewport width",
      improvement: "~200px additional width",
      impact: "positive",
    },
    {
      aspect: "Vertical Space",
      before: "min-h-[calc(100vh-200px)]",
      after: "Full viewport height",
      improvement: "~128px additional height",
      impact: "positive",
    },
    {
      aspect: "Navigation Icons",
      before: "Hidden/commented out",
      after: "Visible with proper spacing",
      improvement: "Better visual hierarchy",
      impact: "positive",
    },
    {
      aspect: "Layout Positioning",
      before: "Relative within AppWrapper",
      after: "Fixed positioning escape",
      improvement: "Complete layout control",
      impact: "positive",
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">Product Configuration Layout Optimization</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Compare the before and after states of the product configuration layout optimization.
          The new layout maximizes screen real estate and improves user experience.
        </p>
        
        {/* View Toggle */}
        <div className="flex items-center justify-center gap-2">
          <Button
            variant={activeView === "before" ? "default" : "outline"}
            onClick={() => setActiveView("before")}
            size="sm"
          >
            Before Optimization
          </Button>
          <ArrowRightIcon className="h-4 w-4 text-muted-foreground" />
          <Button
            variant={activeView === "after" ? "default" : "outline"}
            onClick={() => setActiveView("after")}
            size="sm"
          >
            After Optimization
          </Button>
        </div>
      </div>

      {/* Layout Preview */}
      <Card className="overflow-hidden">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {activeView === "before" ? "Before" : "After"} Optimization
                <Badge variant={activeView === "before" ? "secondary" : "default"}>
                  {activeView === "before" ? "Constrained" : "Optimized"}
                </Badge>
              </CardTitle>
              <CardDescription>
                {activeView === "before" 
                  ? "Layout constrained by AppWrapper padding and margins"
                  : "Full-screen layout with optimized space utilization"
                }
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {/* Layout Mockup */}
          <div className="relative bg-muted/20 border-t">
            {/* Navbar Mockup */}
            <div className="h-16 bg-background border-b border-border/50 flex items-center px-6">
              <div className="text-sm font-medium">NavBar (72px height)</div>
            </div>
            
            {/* Main Layout */}
            <div className={cn(
              "flex",
              activeView === "before" 
                ? "min-h-[400px] mx-4 md:mx-8 my-6 md:my-8" // Simulating AppWrapper constraints
                : "h-[500px]" // Full height simulation
            )}>
              {/* Sidebar */}
              <div className={cn(
                "w-64 border-r border-border/50 bg-card flex-shrink-0 flex flex-col",
                activeView === "before" ? "bg-muted/20" : "bg-card"
              )}>
                {/* Sidebar Header */}
                <div className={cn(
                  "border-b border-border/50",
                  activeView === "before" ? "p-6" : "px-4 py-6"
                )}>
                  <h3 className="text-lg font-semibold">Configurações</h3>
                  <p className="text-sm text-muted-foreground mt-1">Gerencie seu produto</p>
                </div>
                
                {/* Sidebar Navigation */}
                <nav className={cn(
                  "flex-1 space-y-1",
                  activeView === "before" ? "p-4 space-y-2" : "px-2 py-4 space-y-1"
                )}>
                  {sidebarItems.slice(0, 6).map((item, index) => {
                    const Icon = item.icon;
                    const isActive = index === 0;
                    
                    return (
                      <div
                        key={item.id}
                        className={cn(
                          "flex items-center gap-3 rounded-lg text-sm font-medium transition-all duration-200",
                          activeView === "before" ? "px-3 py-2.5" : "px-3 py-3 mx-2",
                          isActive
                            ? "bg-primary text-primary-foreground shadow-sm"
                            : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
                        )}
                      >
                        {activeView === "after" && <Icon className="h-4 w-4 flex-shrink-0" />}
                        <span className="truncate text-xs">{item.label}</span>
                      </div>
                    );
                  })}
                </nav>
                
                {/* Sidebar Footer */}
                <div className={cn(
                  "border-t border-border/50",
                  activeView === "before" ? "p-4" : "px-4 py-3"
                )}>
                  <div className="text-xs text-muted-foreground text-center">
                    <p>Configurações | set</p>
                  </div>
                </div>
              </div>
              
              {/* Main Content Area */}
              <div className="flex-1 flex flex-col overflow-hidden bg-background">
                {/* Content Header */}
                <div className="border-b border-border/50 bg-background/95 backdrop-blur">
                  <div className="flex items-center justify-between px-6 py-4">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>Produtos</span>
                      <span>/</span>
                      <span>Curso Marketing Digital</span>
                      <span>/</span>
                      <span className="text-foreground font-medium">Ofertas</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <div className="text-sm font-medium">R$ 0,00 faturado</div>
                        <div className="text-xs text-muted-foreground">0 venda realizada</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Content Body */}
                <div className="flex-1 overflow-auto p-6">
                  <div className="space-y-6">
                    <div>
                      <h1 className="text-2xl font-bold">Ofertas</h1>
                      <p className="text-muted-foreground">Gerencie as ofertas e promoções do seu produto</p>
                    </div>
                    
                    <Card>
                      <CardHeader>
                        <CardTitle>Ofertas Ativas</CardTitle>
                        <CardDescription>Configure ofertas especiais para aumentar suas vendas</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-8">
                          <div className="w-12 h-12 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-3">
                            <PackageIcon className="h-6 w-6 text-muted-foreground" />
                          </div>
                          <h3 className="font-semibold mb-2">Nenhuma oferta criada</h3>
                          <p className="text-sm text-muted-foreground mb-4">
                            Crie sua primeira oferta para começar a vender mais
                          </p>
                          <Button size="sm">Criar Primeira Oferta</Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Improvements Table */}
      <Card>
        <CardHeader>
          <CardTitle>Optimization Improvements</CardTitle>
          <CardDescription>
            Detailed comparison of the layout changes and their impact on user experience
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {improvements.map((improvement, index) => (
              <div key={index} className="flex items-start gap-4 p-4 border rounded-lg">
                <div className="flex-shrink-0 mt-1">
                  {improvement.impact === "positive" ? (
                    <CheckIcon className="h-5 w-5 text-green-600" />
                  ) : (
                    <XIcon className="h-5 w-5 text-red-600" />
                  )}
                </div>
                <div className="flex-1 space-y-2">
                  <div className="font-medium">{improvement.aspect}</div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Before: </span>
                      <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                        {improvement.before}
                      </span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">After: </span>
                      <span className="font-mono text-xs bg-primary/10 px-2 py-1 rounded">
                        {improvement.after}
                      </span>
                    </div>
                  </div>
                  <div className="text-sm text-green-600 font-medium">
                    ✓ {improvement.improvement}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Benefits Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Key Benefits</CardTitle>
          <CardDescription>
            The optimization delivers significant improvements in user experience and productivity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-primary mb-2">40%</div>
              <div className="text-sm text-muted-foreground">More Workspace</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-2">200px</div>
              <div className="text-sm text-muted-foreground">Additional Width</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-2">128px</div>
              <div className="text-sm text-muted-foreground">Additional Height</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-orange-600 mb-2">33%</div>
              <div className="text-sm text-muted-foreground">Less Padding</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
