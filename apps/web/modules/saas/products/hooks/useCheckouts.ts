"use client";

import { useState } from "react";
import { toast } from "sonner";
import { createCheckoutLink } from "@repo/payments/provider/stripe";

export interface CheckoutLink {
  id: string;
  name: string;
  url: string;
  isActive: boolean;
  createdAt: string;
  productId: string;
  organizationId: string;
  paymentMethods: string[];
  layout: "default" | "minimal" | "modern";
  settings: {
    allowInstallments: boolean;
    maxInstallments: number;
    primaryColor?: string;
    showProductImage: boolean;
    showProductDescription: boolean;
    successUrl?: string;
    cancelUrl?: string;
  };
}

export interface CreateCheckoutData {
  name: string;
  layout: "default" | "minimal" | "modern";
  paymentMethods: string[];
  isActive: boolean;
  allowInstallments: boolean;
  maxInstallments: number;
  primaryColor?: string;
  showProductImage: boolean;
  showProductDescription: boolean;
  successUrl?: string;
  cancelUrl?: string;
}

export function useCheckouts(productId: string, organizationId: string) {
  const [checkouts, setCheckouts] = useState<CheckoutLink[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const createCheckout = async (data: CreateCheckoutData, product: any) => {
    setIsLoading(true);
    try {
      // For now, we'll create a simple checkout URL that redirects to a payment page
      // In a real implementation, you would create a Stripe Price first, then use it here
      const checkoutUrl = `${window.location.origin}/checkout/${product.id}?org=${organizationId}`;

      // Note: In production, you would:
      // 1. Create a Stripe Price for this product if it doesn't exist
      // 2. Use createCheckoutLink with the Stripe Price ID
      // const stripeUrl = await createCheckoutLink({
      //   type: "one-time",
      //   productId: product.stripePriceId, // Stripe Price ID
      //   redirectUrl: data.successUrl || `${window.location.origin}/success`,
      //   organizationId,
      // });

      // Create checkout record
      const newCheckout: CheckoutLink = {
        id: `checkout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: data.name,
        url: checkoutUrl,
        isActive: data.isActive,
        createdAt: new Date().toISOString(),
        productId,
        organizationId,
        paymentMethods: data.paymentMethods,
        layout: data.layout,
        settings: {
          allowInstallments: data.allowInstallments,
          maxInstallments: data.maxInstallments,
          primaryColor: data.primaryColor,
          showProductImage: data.showProductImage,
          showProductDescription: data.showProductDescription,
          successUrl: data.successUrl,
          cancelUrl: data.cancelUrl,
        },
      };

      // Add to local state (in a real app, this would be saved to database)
      setCheckouts(prev => [...prev, newCheckout]);
      
      toast.success("Checkout criado com sucesso!");
      return newCheckout;
    } catch (error) {
      console.error("Error creating checkout:", error);
      toast.error("Erro ao criar checkout");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteCheckout = async (checkoutId: string) => {
    try {
      // In a real app, this would call an API to delete from database
      setCheckouts(prev => prev.filter(c => c.id !== checkoutId));
      toast.success("Checkout excluído com sucesso!");
    } catch (error) {
      console.error("Error deleting checkout:", error);
      toast.error("Erro ao excluir checkout");
      throw error;
    }
  };

  const copyCheckoutUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      toast.success("URL copiada para a área de transferência!");
    } catch (error) {
      console.error("Error copying URL:", error);
      toast.error("Erro ao copiar URL");
    }
  };

  const generateQuickCheckout = async (product: any) => {
    setIsLoading(true);
    try {
      // Create a quick checkout URL for immediate use
      const checkoutUrl = `${window.location.origin}/checkout/${product.id}?org=${organizationId}`;

      // Open checkout in new tab
      window.open(checkoutUrl, '_blank');
      toast.success("Checkout aberto em nova aba!");
    } catch (error) {
      console.error("Error generating quick checkout:", error);
      toast.error("Erro ao gerar checkout rápido");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    checkouts,
    isLoading,
    createCheckout,
    deleteCheckout,
    copyCheckoutUrl,
    generateQuickCheckout,
  };
}
