"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";

export interface CheckoutLink {
  id: string;
  name: string;
  url: string;
  isActive: boolean;
  createdAt: string;
  productId: string;
  organizationId: string;
  paymentMethods: string[];
  layout: "default" | "minimal" | "modern";
  settings: {
    allowInstallments: boolean;
    maxInstallments: number;
    primaryColor?: string;
    showProductImage: boolean;
    showProductDescription: boolean;
    successUrl?: string;
    cancelUrl?: string;
    checkoutType?: 'DEFAULT' | 'CUSTOM' | 'EXTERNAL';
    customCheckoutUrl?: string;
    termsUrl?: string;
  };
}

export interface CreateCheckoutData {
  name: string;
  layout: "default" | "minimal" | "modern";
  paymentMethods: string[];
  isActive: boolean;
  allowInstallments: boolean;
  maxInstallments: number;
  primaryColor?: string;
  showProductImage: boolean;
  showProductDescription: boolean;
  successUrl?: string;
  cancelUrl?: string;
  checkoutType?: 'DEFAULT' | 'CUSTOM' | 'EXTERNAL';
  customCheckoutUrl?: string;
  termsUrl?: string;
}

export function useCheckouts(productId: string, organizationId: string) {
  const [checkouts, setCheckouts] = useState<CheckoutLink[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load checkouts from API
  const loadCheckouts = async () => {
    try {
      const response = await fetch(`/api/products/${productId}/checkouts`);
      if (response.ok) {
        const data = await response.json();
        const formattedCheckouts = data.checkouts.map((checkout: any) => {
          const customParams = checkout.customParams ? JSON.parse(checkout.customParams) : {};
          return {
            id: checkout.id,
            name: customParams.name || "Checkout",
            url: checkout.url,
            isActive: customParams.isActive ?? true,
            createdAt: checkout.createdAt,
            productId: checkout.productId,
            organizationId,
            paymentMethods: customParams.paymentMethods || ["credit_card"],
            layout: customParams.layout || "default",
            settings: {
              allowInstallments: customParams.allowInstallments ?? true,
              maxInstallments: customParams.maxInstallments ?? 12,
              primaryColor: customParams.primaryColor,
              showProductImage: customParams.showProductImage ?? true,
              showProductDescription: customParams.showProductDescription ?? true,
              successUrl: customParams.successUrl,
              cancelUrl: customParams.cancelUrl,
              checkoutType: customParams.checkoutType || "DEFAULT",
              customCheckoutUrl: customParams.customCheckoutUrl,
              termsUrl: customParams.termsUrl,
            },
          };
        });
        setCheckouts(formattedCheckouts);
      }
    } catch (error) {
      console.error("Error loading checkouts:", error);
    }
  };

  // Load checkouts on mount
  useEffect(() => {
    loadCheckouts();
  }, [productId]);

  const createCheckout = async (data: CreateCheckoutData) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/products/${productId}/checkouts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to create checkout');
      }

      const result = await response.json();

      // Reload checkouts to get the updated list
      await loadCheckouts();

      toast.success("Checkout criado com sucesso!");
      return result.checkout;
    } catch (error) {
      console.error("Error creating checkout:", error);
      toast.error("Erro ao criar checkout");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteCheckout = async (checkoutId: string) => {
    try {
      const response = await fetch(`/api/products/${productId}/checkouts/${checkoutId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete checkout');
      }

      // Remove from local state
      setCheckouts(prev => prev.filter(c => c.id !== checkoutId));
      toast.success("Checkout excluído com sucesso!");
    } catch (error) {
      console.error("Error deleting checkout:", error);
      toast.error("Erro ao excluir checkout");
      throw error;
    }
  };

  const copyCheckoutUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      toast.success("URL copiada para a área de transferência!");
    } catch (error) {
      console.error("Error copying URL:", error);
      toast.error("Erro ao copiar URL");
    }
  };

  const generateQuickCheckout = async () => {
    setIsLoading(true);
    try {
      // Create a quick checkout URL using the existing multi-gateway system
      const checkoutUrl = `${window.location.origin}/checkout/${productId}?org=${organizationId}&quick=true`;

      // Open checkout in new tab
      window.open(checkoutUrl, '_blank');
      toast.success("Checkout rápido aberto em nova aba!");
    } catch (error) {
      console.error("Error generating quick checkout:", error);
      toast.error("Erro ao gerar checkout rápido");
    } finally {
      setIsLoading(false);
    }
  };

  const saveCheckoutConfiguration = async (checkoutId: string, config: Partial<CreateCheckoutData>) => {
    try {
      // In a real implementation, this would save to the database
      // For now, we'll update the local state
      setCheckouts(prev => prev.map(checkout =>
        checkout.id === checkoutId
          ? {
              ...checkout,
              settings: { ...checkout.settings, ...config }
            }
          : checkout
      ));
      toast.success("Configuração salva com sucesso!");
    } catch (error) {
      console.error("Error saving checkout configuration:", error);
      toast.error("Erro ao salvar configuração");
      throw error;
    }
  };

  return {
    checkouts,
    isLoading,
    createCheckout,
    deleteCheckout,
    copyCheckoutUrl,
    generateQuickCheckout,
    saveCheckoutConfiguration,
  };
}
