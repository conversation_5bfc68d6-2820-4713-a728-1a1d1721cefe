"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@ui/components/chart";
import { Area, AreaChart, Bar, BarChart, CartesianGrid, Line, LineChart, XAxis, YAxis } from "recharts";
import { ChartData } from "../types";

interface RevenueChartProps {
  data: ChartData[];
  type?: 'bar' | 'line' | 'area';
  height?: number;
  className?: string;
}

export function RevenueChart({
  data,
  type = 'bar',
  height = 300,
  className
}: RevenueChartProps) {
  const maxValue = Math.max(...data.map(d => d.value));

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Receita por Período</CardTitle>
        <CardDescription>
          Evolução da receita ao longo do tempo
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            revenue: {
              label: "<PERSON><PERSON><PERSON>",
              color: "hsl(var(--chart-1))",
            },
          }}
          className="w-full"
          style={{ minHeight: `${height}px` }}
        >
          {type === 'bar' ? (
            <BarChart data={data} accessibilityLayer>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="label"
                tickLine={false}
                axisLine={false}
                tickMargin={10}
                className="text-xs fill-muted-foreground"
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                tickMargin={10}
                tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
                className="text-xs fill-muted-foreground"
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelFormatter={(value) => `Período: ${value}`}
                    formatter={(value) => [
                      `R$ ${Number(value).toLocaleString()}`,
                      "Receita"
                    ]}
                  />
                }
              />
              <Bar
                dataKey="value"
                fill="#4e6df5"
                radius={4}
              />
            </BarChart>
          ) : type === 'line' ? (
            <LineChart data={data} accessibilityLayer>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="label"
                tickLine={false}
                axisLine={false}
                tickMargin={10}
                className="text-xs fill-muted-foreground"
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                tickMargin={10}
                tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
                className="text-xs fill-muted-foreground"
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelFormatter={(value) => `Período: ${value}`}
                    formatter={(value) => [
                      `R$ ${Number(value).toLocaleString()}`,
                      "Receita"
                    ]}
                  />
                }
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#4e6df5"
                strokeWidth={2}
                dot={{ fill: "#4e6df5", strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          ) : (
            <AreaChart data={data} accessibilityLayer>
              <defs>
                <linearGradient id="fillRevenue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#4e6df5" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#4e6df5" stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="label"
                tickLine={false}
                axisLine={false}
                tickMargin={10}
                className="text-xs fill-muted-foreground"
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                tickMargin={10}
                tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
                className="text-xs fill-muted-foreground"
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelFormatter={(value) => `Período: ${value}`}
                    formatter={(value) => [
                      `R$ ${Number(value).toLocaleString()}`,
                      "Receita"
                    ]}
                  />
                }
              />
              <Area
                type="monotone"
                dataKey="value"
                stroke="#4e6df5"
                fill="url(#fillRevenue)"
                strokeWidth={2}
              />
            </AreaChart>
          )}
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
